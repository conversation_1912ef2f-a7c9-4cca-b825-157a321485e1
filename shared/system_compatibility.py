"""
系统兼容性检测模块
根据不同操作系统自动选择最优的 Celery Worker 配置
注意：GPU相关功能已迁移到各服务的专用模块中
"""

import os
import platform
from typing import Dict, Any, Optional
from dataclasses import dataclass

from shared.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class SystemCompatibilityConfig:
    """系统兼容性配置 - 专注于Celery Worker配置"""

    # 系统信息
    system_name: str
    system_version: str

    # Worker 配置
    pool_type: str  # 'processes' 或 'threads'
    concurrency: int
    max_tasks_per_child: Optional[int]

    # 内存管理策略
    memory_strategy: str  # 'process_restart' 或 'code_cleanup'

    # 启动方式
    multiprocessing_start_method: Optional[str]

    # 环境变量（非GPU相关）
    env_vars: Dict[str, str]


class SystemCompatibilityDetector:
    """系统兼容性检测器"""
    
    def __init__(self):
        self.system = platform.system()
        self.system_version = platform.version()
        self.python_version = platform.python_version()
        
        logger.info(f"系统兼容性检测: {self.system} {self.system_version}")
        logger.info(f"Python版本: {self.python_version}")
    
    def detect_optimal_config(self) -> SystemCompatibilityConfig:
        """检测最优配置"""
        
        if self.system == "Darwin":  # macOS
            return self._get_macos_config()
        elif self.system == "Linux":  # Linux
            return self._get_linux_config()
        elif self.system == "Windows":  # Windows
            return self._get_windows_config()
        else:
            logger.warning(f"未知系统类型: {self.system}，使用默认配置")
            return self._get_default_config()
    
    def _get_macos_config(self) -> SystemCompatibilityConfig:
        """macOS 系统配置"""

        # 检查用户偏好的解决方案
        macos_solution = os.getenv("MACOS_FORK_SOLUTION", "env_var").lower()

        if macos_solution == "env_var":
            logger.info("🍎 检测到 macOS 系统，使用环境变量方案解决 fork 冲突")
            return self._get_macos_env_var_config()
        else:
            logger.info("🍎 检测到 macOS 系统，使用线程池方案避免 fork 冲突")
            return self._get_macos_threads_config()

    def _get_macos_threads_config(self) -> SystemCompatibilityConfig:
        """macOS 线程池方案配置"""
        return SystemCompatibilityConfig(
            system_name="macOS (线程池)",
            system_version=self.system_version,

            # 使用线程池避免 fork 冲突
            pool_type="threads",
            concurrency=1,  # 线程池建议单线程，避免模型并发问题
            max_tasks_per_child=None,  # 线程池中无效

            # 依赖代码清理，不能依赖进程重启
            memory_strategy="code_cleanup",

            # macOS 不需要特殊启动方式
            multiprocessing_start_method=None,

            # 环境变量（非GPU相关）
            env_vars={}
        )

    def _get_macos_env_var_config(self) -> SystemCompatibilityConfig:
        """macOS 环境变量方案配置"""
        return SystemCompatibilityConfig(
            system_name="macOS (环境变量)",
            system_version=self.system_version,

            # 使用进程池，通过环境变量解决 fork 冲突
            pool_type="processes",
            concurrency=1,
            max_tasks_per_child=10,  # 定期重启清理内存

            # 依赖进程重启清理内存
            memory_strategy="process_restart",

            # macOS 使用 fork 启动
            multiprocessing_start_method="fork",

            # 关键环境变量：禁用 fork 安全检查
            env_vars={
                "OBJC_DISABLE_INITIALIZE_FORK_SAFETY": "YES",  # 禁用 fork 安全检查
            }
        )
    
    def _get_linux_config(self) -> SystemCompatibilityConfig:
        """Linux 系统配置"""
        logger.info("🐧 检测到 Linux 系统，配置进程池模式享受进程隔离优势")

        return SystemCompatibilityConfig(
            system_name="Linux",
            system_version=self.system_version,

            # 使用进程池，享受隔离优势
            pool_type="processes",
            concurrency=1,
            max_tasks_per_child=10,  # 定期重启清理内存

            # 依赖进程重启清理内存
            memory_strategy="process_restart",

            # Linux 使用 fork 启动
            multiprocessing_start_method="fork",

            # 环境变量
            env_vars={}
        )
    
    def _get_windows_config(self) -> SystemCompatibilityConfig:
        """Windows 系统配置"""
        logger.info("🪟 检测到 Windows 系统，配置进程池模式使用 spawn 启动")

        return SystemCompatibilityConfig(
            system_name="Windows",
            system_version=self.system_version,

            # 使用进程池，Windows 自动使用 spawn
            pool_type="processes",
            concurrency=1,
            max_tasks_per_child=10,

            # 依赖进程重启清理内存
            memory_strategy="process_restart",

            # Windows 使用 spawn 启动
            multiprocessing_start_method="spawn",

            # 环境变量
            env_vars={}
        )
    
    def _get_default_config(self) -> SystemCompatibilityConfig:
        """默认配置（未知系统）"""
        logger.warning("使用保守的默认配置")

        return SystemCompatibilityConfig(
            system_name="Unknown",
            system_version=self.system_version,

            # 保守使用线程池
            pool_type="threads",
            concurrency=1,
            max_tasks_per_child=None,

            # 依赖代码清理
            memory_strategy="code_cleanup",

            # 不设置启动方式
            multiprocessing_start_method=None,

            # 环境变量
            env_vars={}
        )
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息（非GPU相关）"""
        info = {
            "system": self.system,
            "system_version": self.system_version,
            "python_version": self.python_version,
        }

        return info
    
    def apply_environment_variables(self, config: SystemCompatibilityConfig):
        """应用环境变量"""
        for key, value in config.env_vars.items():
            os.environ[key] = value
            logger.info(f"设置环境变量: {key}={value}")
    
    def log_compatibility_info(self, config: SystemCompatibilityConfig):
        """记录兼容性信息"""
        logger.info("=" * 60)
        logger.info("🔧 系统兼容性配置")
        logger.info("=" * 60)
        logger.info(f"系统: {config.system_name}")
        logger.info(f"Worker池类型: {config.pool_type}")
        logger.info(f"并发数: {config.concurrency}")
        logger.info(f"任务重启阈值: {config.max_tasks_per_child}")
        logger.info(f"内存策略: {config.memory_strategy}")
        logger.info(f"启动方式: {config.multiprocessing_start_method}")

        if config.env_vars:
            logger.info("环境变量:")
            for key, value in config.env_vars.items():
                logger.info(f"  {key}={value}")

        # 系统信息
        system_info = self.get_system_info()
        logger.info("系统信息:")
        for key, value in system_info.items():
            logger.info(f"  {key}: {value}")

        logger.info("=" * 60)


def show_macos_solution_guide():
    """显示 macOS 解决方案指南"""
    if platform.system() != "Darwin":
        return

    current_solution = os.getenv("MACOS_FORK_SOLUTION", "threads")
    solutions = {
        "threads": "线程池方案 - 彻底避免 fork 冲突，架构清晰，安全可靠",
        "env_var": "环境变量方案 - 保持进程池，配置简单，但绕过系统安全检查"
    }

    logger.info("=" * 60)
    logger.info("🍎 macOS fork 冲突解决方案")
    logger.info("=" * 60)
    logger.info(f"当前方案: {current_solution}")
    logger.info("")
    logger.info("可用方案:")
    for key, desc in solutions.items():
        marker = "👉" if key == current_solution else "  "
        logger.info(f"{marker} {key}: {desc}")

    logger.info("")
    logger.info("切换方案:")
    logger.info("export MACOS_FORK_SOLUTION=threads    # 使用线程池方案")
    logger.info("export MACOS_FORK_SOLUTION=env_var    # 使用环境变量方案")
    logger.info("=" * 60)


# 全局实例
compatibility_detector = SystemCompatibilityDetector()

# 显示 macOS 解决方案指南（如果是 macOS 系统）
if platform.system() == "Darwin":
    show_macos_solution_guide()

system_config = compatibility_detector.detect_optimal_config()

# 应用环境变量
compatibility_detector.apply_environment_variables(system_config)

# 记录配置信息
compatibility_detector.log_compatibility_info(system_config)


def get_system_config() -> SystemCompatibilityConfig:
    """获取系统配置"""
    return system_config


def get_celery_worker_args() -> Dict[str, Any]:
    """获取 Celery Worker 启动参数"""
    config = get_system_config()
    
    args = {
        "pool": config.pool_type,
        "concurrency": config.concurrency,
    }
    
    # 只有进程池才设置 max_tasks_per_child
    if config.pool_type == "processes" and config.max_tasks_per_child:
        args["max_tasks_per_child"] = config.max_tasks_per_child
    
    return args


def get_memory_strategy() -> str:
    """获取内存管理策略"""
    config = get_system_config()
    return config.memory_strategy


def get_macos_solutions() -> Dict[str, str]:
    """获取 macOS 可用的解决方案"""
    return {
        "threads": "线程池方案 - 彻底避免 fork 冲突，架构清晰，安全可靠",
        "env_var": "环境变量方案 - 保持进程池，配置简单，但绕过系统安全检查"
    }


def set_macos_solution(solution: str):
    """设置 macOS 解决方案"""
    if solution not in ["threads", "env_var"]:
        raise ValueError(f"无效的解决方案: {solution}，可选值: threads, env_var")

    os.environ["MACOS_FORK_SOLUTION"] = solution
    logger.info(f"设置 macOS 解决方案: {solution}")
