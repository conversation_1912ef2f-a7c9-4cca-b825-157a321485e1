"""
遥测禁用工具
在导入任何可能包含遥测功能的模块之前调用，确保隐私保护
"""

import os
import sys


def disable_all_telemetry():
    """
    禁用所有已知的遥测和分析服务
    包括PostHog、Lang<PERSON><PERSON><PERSON>、以及其他常见的分析服务
    """

    # PostHog遥测禁用 - 更全面的设置
    os.environ.setdefault("POSTHOG_HOST", "")
    os.environ.setdefault("POSTHOG_PROJECT_ID", "")
    os.environ.setdefault("POSTHOG_API_KEY", "")
    os.environ.setdefault("POSTHOG_FEATURE_FLAGS", "false")
    os.environ.setdefault("POSTHOG_DISABLED", "true")
    os.environ.setdefault("DISABLE_POSTHOG", "true")

    # LangChain遥测禁用 - 更严格的设置
    os.environ.setdefault("LANGCHAIN_TRACING_V2", "false")
    os.environ.setdefault("LANGCHAIN_ANALYTICS", "false")
    os.environ.setdefault("LANGCHAIN_TRACING", "false")
    os.environ.setdefault("LANGCHAIN_TRACKING", "false")
    os.environ.setdefault("LANGCHAIN_ENDPOINT", "")
    os.environ.setdefault("LANGCHAIN_API_KEY", "")
    os.environ.setdefault("LANGCHAIN_PROJECT", "")
    os.environ.setdefault("LANGCHAIN_SESSION", "")
    os.environ.setdefault("LANGCHAIN_CALLBACKS_MANAGER", "")

    # 通用遥测禁用
    os.environ.setdefault("TELEMETRY_DISABLED", "true")
    os.environ.setdefault("DO_NOT_TRACK", "1")
    os.environ.setdefault("ANALYTICS_DISABLED", "true")

    # 其他可能的遥测服务
    os.environ.setdefault("SENTRY_DSN", "")
    os.environ.setdefault("MIXPANEL_TOKEN", "")
    os.environ.setdefault("AMPLITUDE_API_KEY", "")
    os.environ.setdefault("SEGMENT_WRITE_KEY", "")

    # OpenAI遥测禁用
    os.environ.setdefault("OPENAI_LOG_LEVEL", "CRITICAL")

    # HuggingFace遥测禁用
    os.environ.setdefault("HF_HUB_DISABLE_TELEMETRY", "1")
    os.environ.setdefault("TRANSFORMERS_OFFLINE", "1")


def disable_langchain_telemetry():
    """
    专门禁用LangChain遥测功能
    在导入LangChain之前调用
    """
    # 设置环境变量
    langchain_env_vars = {
        "LANGCHAIN_TRACING_V2": "false",
        "LANGCHAIN_ANALYTICS": "false",
        "LANGCHAIN_TRACING": "false",
        "LANGCHAIN_TRACKING": "false",
        "LANGCHAIN_ENDPOINT": "",
        "LANGCHAIN_API_KEY": "",
        "LANGCHAIN_PROJECT": "",
        "LANGCHAIN_SESSION": "",
        "LANGCHAIN_CALLBACKS_MANAGER": "",
        "LANGCHAIN_HUB_API_KEY": "",
        "LANGCHAIN_HUB_API_URL": "",
    }

    for key, value in langchain_env_vars.items():
        os.environ.setdefault(key, value)

    # 尝试在运行时禁用LangChain遥测
    try:
        # 如果LangChain已经导入，尝试禁用其遥测
        if 'langchain' in sys.modules:
            _disable_langchain_runtime_telemetry()
    except Exception:
        # 静默处理，避免影响主要功能
        pass


def _disable_langchain_runtime_telemetry():
    """
    运行时禁用LangChain遥测
    """
    try:
        # 尝试禁用LangChain Core的遥测
        if 'langchain_core' in sys.modules:
            import langchain_core
            # 设置全局禁用标志
            if hasattr(langchain_core, '_DISABLE_TELEMETRY'):
                langchain_core._DISABLE_TELEMETRY = True

        # 尝试禁用LangChain的回调管理器
        if 'langchain_core.callbacks' in sys.modules:
            from langchain_core.callbacks import manager
            if hasattr(manager, 'set_default_callback_manager'):
                # 设置空的回调管理器
                manager.set_default_callback_manager(None)

    except Exception:
        # 静默处理导入错误
        pass


def block_posthog_network():
    """
    在网络层面阻断PostHog连接
    """
    try:
        import socket
        original_getaddrinfo = socket.getaddrinfo

        def patched_getaddrinfo(host, port, *args, **kwargs):
            # 阻断PostHog相关域名
            if host and any(domain in host.lower() for domain in [
                'posthog.com', 'app.posthog.com', 'us.posthog.com', 'eu.posthog.com',
                'us.i.posthog.com', 'eu.i.posthog.com', 'api.posthog.com'
            ]):
                raise socket.gaierror(f"[BLOCKED] PostHog connection to {host} blocked")
            return original_getaddrinfo(host, port, *args, **kwargs)

        socket.getaddrinfo = patched_getaddrinfo

    except Exception:
        # 静默处理网络阻断设置失败
        pass


# 在模块导入时立即禁用遥测
disable_all_telemetry()
disable_langchain_telemetry()
block_posthog_network()