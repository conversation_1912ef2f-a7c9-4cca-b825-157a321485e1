"""
遥测禁用工具
在导入任何可能包含遥测功能的模块之前调用，确保隐私保护
"""

import os


def disable_all_telemetry():
    """
    禁用所有已知的遥测和分析服务
    包括PostHog、<PERSON><PERSON><PERSON><PERSON>、以及其他常见的分析服务
    """
    
    # PostHog遥测禁用
    os.environ.setdefault("POSTHOG_HOST", "")
    os.environ.setdefault("POSTHOG_PROJECT_ID", "")
    os.environ.setdefault("POSTHOG_API_KEY", "")
    os.environ.setdefault("POSTHOG_FEATURE_FLAGS", "false")
    
    # LangChain遥测禁用
    os.environ.setdefault("LANGCHAIN_TRACING_V2", "false")
    os.environ.setdefault("LANGCHAIN_ANALYTICS", "false")
    os.environ.setdefault("LANGCHAIN_TRACING", "false")
    os.environ.setdefault("LANGCHAIN_TRACKING", "false")
    os.environ.setdefault("LANGCHAIN_ENDPOINT", "")
    os.environ.setdefault("LANGCHAIN_API_KEY", "")
    
    # 通用遥测禁用
    os.environ.setdefault("TELEMETRY_DISABLED", "true")
    os.environ.setdefault("DO_NOT_TRACK", "1")
    os.environ.setdefault("ANALYTICS_DISABLED", "true")
    
    # 其他可能的遥测服务
    os.environ.setdefault("SENTRY_DSN", "")
    os.environ.setdefault("MIXPANEL_TOKEN", "")
    os.environ.setdefault("AMPLITUDE_API_KEY", "")
    os.environ.setdefault("SEGMENT_WRITE_KEY", "")
    
    # OpenAI遥测禁用
    os.environ.setdefault("OPENAI_LOG_LEVEL", "CRITICAL")
    
    # HuggingFace遥测禁用
    os.environ.setdefault("HF_HUB_DISABLE_TELEMETRY", "1")
    os.environ.setdefault("TRANSFORMERS_OFFLINE", "1")


# 在模块导入时立即禁用遥测
disable_all_telemetry()