"""
LLM模块专用遥测禁用工具
在导入任何LangChain相关模块之前调用，确保完全禁用遥测
"""

import os
import sys


def disable_all_telemetry_for_llm():
    """
    为LLM模块完全禁用所有遥测功能
    包括PostHog、Lang<PERSON>hai<PERSON>、以及其他分析服务
    """
    
    # PostHog遥测完全禁用
    posthog_vars = {
        "POSTHOG_HOST": "",
        "POSTHOG_PROJECT_ID": "",
        "POSTHOG_API_KEY": "",
        "POSTHOG_FEATURE_FLAGS": "false",
        "POSTHOG_DISABLED": "true",
        "DISABLE_POSTHOG": "true",
    }
    
    # LangChain遥测完全禁用
    langchain_vars = {
        "LANGCHAIN_TRACING_V2": "false",
        "LANGCHAIN_ANALYTICS": "false",
        "LANGCHAIN_TRACING": "false",
        "LANGCHAIN_TRACKING": "false",
        "LANGCHAIN_ENDPOINT": "",
        "LANGCHAIN_API_KEY": "",
        "LANGCHAIN_PROJECT": "",
        "LANGCHAIN_SESSION": "",
        "LANGCHAIN_CALLBACKS_MANAGER": "",
        "LANGCHAIN_HUB_API_KEY": "",
        "LANGCHAIN_HUB_API_URL": "",
    }
    
    # 通用遥测禁用
    general_vars = {
        "TELEMETRY_DISABLED": "true",
        "DO_NOT_TRACK": "1",
        "ANALYTICS_DISABLED": "true",
    }
    
    # 其他遥测服务禁用
    other_vars = {
        "SENTRY_DSN": "",
        "MIXPANEL_TOKEN": "",
        "AMPLITUDE_API_KEY": "",
        "SEGMENT_WRITE_KEY": "",
        "OPENAI_LOG_LEVEL": "CRITICAL",
        "HF_HUB_DISABLE_TELEMETRY": "1",
        "TRANSFORMERS_OFFLINE": "1",
    }
    
    # 合并所有环境变量
    all_vars = {**posthog_vars, **langchain_vars, **general_vars, **other_vars}
    
    # 设置环境变量
    for key, value in all_vars.items():
        os.environ.setdefault(key, value)
    
    # 网络级别阻断PostHog连接
    _block_posthog_network()
    
    # 运行时禁用LangChain遥测
    _disable_langchain_runtime()


def _block_posthog_network():
    """
    在网络层面阻断PostHog连接
    """
    try:
        import socket
        original_getaddrinfo = socket.getaddrinfo
        
        def patched_getaddrinfo(host, port, *args, **kwargs):
            # 阻断PostHog相关域名
            if host and any(domain in host.lower() for domain in [
                'posthog.com', 'app.posthog.com', 'us.posthog.com', 'eu.posthog.com',
                'us.i.posthog.com', 'eu.i.posthog.com', 'api.posthog.com',
                'analytics.posthog.com', 'events.posthog.com'
            ]):
                raise socket.gaierror(f"[BLOCKED] PostHog connection to {host} blocked by LLM telemetry disable")
            return original_getaddrinfo(host, port, *args, **kwargs)
        
        socket.getaddrinfo = patched_getaddrinfo
        
    except Exception:
        # 静默处理网络阻断设置失败
        pass


def _disable_langchain_runtime():
    """
    运行时禁用LangChain遥测
    """
    try:
        # 如果LangChain已经导入，尝试禁用其遥测
        if 'langchain_core' in sys.modules:
            import langchain_core
            # 设置全局禁用标志
            if hasattr(langchain_core, '_DISABLE_TELEMETRY'):
                langchain_core._DISABLE_TELEMETRY = True
        
        # 尝试禁用LangChain的回调管理器
        if 'langchain_core.callbacks' in sys.modules:
            from langchain_core.callbacks import manager
            if hasattr(manager, 'set_default_callback_manager'):
                # 设置空的回调管理器
                manager.set_default_callback_manager(None)
                
    except Exception:
        # 静默处理导入错误
        pass


# 在模块导入时立即执行
disable_all_telemetry_for_llm()
