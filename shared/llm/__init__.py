"""
Shared LLM模块 - 统一的LLM接口和工厂
提供跨服务的LLM访问和管理
"""

# 在导入任何可能包含LangChain的模块之前完全禁用遥测
from .telemetry_disable_llm import disable_all_telemetry_for_llm
disable_all_telemetry_for_llm()

from .base import (
    BaseLLM,
    LLMConfig,
    LLMResponse,
    LLMProvider,
    LLMError,
    LLMConnectionError,
    LLMAuthenticationError,
    LLMRateLimitError,
    LLMInvalidRequestError,
)

from .factory import (
    LLMFactory,
    ConfigBuilder,
    create_llm_from_settings,
    register_llm_provider,
    list_available_providers,
    get_factory_info,
)

from .langchain_adapter import (
    LangChainLLMAdapter,
    create_langchain_llm,
)

# 导入所有提供商以确保它们被注册
from . import providers

__all__ = [
    # 基础类和枚举
    "BaseLLM",
    "LLMConfig", 
    "LLMResponse",
    "LLMProvider",
    # 异常类
    "LLMError",
    "LLMConnectionError",
    "LLMAuthenticationError",
    "LLMRateLimitError",
    "LLMInvalidRequestError",
    # 工厂类和函数
    "LLMFactory",
    "ConfigBuilder",
    "create_llm_from_settings",
    "register_llm_provider",
    "list_available_providers",
    "get_factory_info",
    # LangChain适配器
    "LangChainLLMAdapter",
    "create_langchain_llm",
] 