"""
Shared LLM模块 - 统一的LLM接口和工厂
提供跨服务的LLM访问和管理
"""

import os

# 在导入任何可能包含LangChain的模块之前禁用遥测
os.environ.setdefault("POSTHOG_HOST", "")
os.environ.setdefault("POSTHOG_PROJECT_ID", "")
os.environ.setdefault("POSTHOG_API_KEY", "")
os.environ.setdefault("POSTHOG_FEATURE_FLAGS", "false")
os.environ.setdefault("LANGCHAIN_TRACING_V2", "false")
os.environ.setdefault("LANGCHAIN_ANALYTICS", "false")
os.environ.setdefault("LANGCHAIN_TRACING", "false")
os.environ.setdefault("LANGCHAIN_TRACKING", "false")
os.environ.setdefault("TELEMETRY_DISABLED", "true")
os.environ.setdefault("DO_NOT_TRACK", "1")

from .base import (
    BaseLLM,
    LLMConfig,
    LLMResponse,
    LLMProvider,
    LLMError,
    LLMConnectionError,
    LLMAuthenticationError,
    LLMRateLimitError,
    LLMInvalidRequestError,
)

from .factory import (
    LLMFactory,
    ConfigBuilder,
    create_llm_from_settings,
    register_llm_provider,
    list_available_providers,
    get_factory_info,
)

from .langchain_adapter import (
    LangChainLLMAdapter,
    create_langchain_llm,
)

# 导入所有提供商以确保它们被注册
from . import providers

__all__ = [
    # 基础类和枚举
    "BaseLLM",
    "LLMConfig", 
    "LLMResponse",
    "LLMProvider",
    # 异常类
    "LLMError",
    "LLMConnectionError",
    "LLMAuthenticationError",
    "LLMRateLimitError",
    "LLMInvalidRequestError",
    # 工厂类和函数
    "LLMFactory",
    "ConfigBuilder",
    "create_llm_from_settings",
    "register_llm_provider",
    "list_available_providers",
    "get_factory_info",
    # LangChain适配器
    "LangChainLLMAdapter",
    "create_langchain_llm",
] 