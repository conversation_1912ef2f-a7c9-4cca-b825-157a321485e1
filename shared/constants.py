"""
共享常量定义
定义微服务架构中使用的常量
"""

from enum import Enum


class ServiceNames:
    """服务名称常量"""
    DEEP_SERVICE = "deep-service"
    EMBEDDING_SERVICE = "embedding-service"
    WORKER_SERVICE = "worker-service"


class ServicePorts:
    """默认服务端口"""
    DEEP_SERVICE = 8000
    EMBEDDING_SERVICE = 8004
    CHROMADB = 8001
    REDIS = 6379
    FLOWER = 5555


class QueueNames:
    """Celery队列名称"""
    DEFAULT = "default"
    VECTORIZATION = "vectorization"
    ANALYSIS = "analysis"
    HEALTH = "health"


class TaskNames:
    """Celery任务名称"""
    VECTORIZE_FILE = "worker.tasks.vectorize_file"
    ANALYZE_RISK = "worker.tasks.analyze_risk"
    HEALTH_CHECK = "worker.tasks.health_check"


class FileStatus(str, Enum):
    """文件状态枚举"""
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    VECTORIZED = "vectorized"
    FAILED = "failed"
    DELETED = "deleted"


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "PENDING"
    STARTED = "STARTED"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    RETRY = "RETRY"
    REVOKED = "REVOKED"


class AnalysisStatus(str, Enum):
    """分析状态枚举"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class RiskLevel(str, Enum):
    """风险等级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class LLMProvider(str, Enum):
    """LLM提供商枚举"""
    DEEPSEEK = "deepseek"
    OPENAI = "openai"
    CLAUDE = "claude"
    GEMINI = "gemini"


class DeviceType(str, Enum):
    """设备类型枚举"""
    AUTO = "auto"
    CPU = "cpu"
    CUDA = "cuda"
    MPS = "mps"


class LogLevel(str, Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class Environment(str, Enum):
    """环境类型枚举"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


# API路径常量
class APIRoutes:
    """API路径常量"""
    
    # Deep Service
    HEALTH = "/health"
    UPLOAD = "/upload"
    ANALYZE = "/analyze"
    STATUS = "/status"
    FILES = "/files"
    TASKS = "/tasks"
    RESULTS = "/results"
    
    # Embedding Service
    EMBED_QUERY = "/api/v1/embed/query"
    EMBED_DOCUMENTS = "/api/v1/embed/documents"
    EMBED_HEALTH = "/api/v1/health"
    EMBED_MODEL_INFO = "/api/v1/embed/model/info"


# 文件类型常量
class FileTypes:
    """支持的文件类型"""
    ALLOWED_EXTENSIONS = {".csv", ".xlsx", ".xls"}
    MIME_TYPES = {
        ".csv": "text/csv",
        ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ".xls": "application/vnd.ms-excel",
    }


# 配置常量
class ConfigDefaults:
    """默认配置值"""
    
    # 文件上传
    MAX_FILE_SIZE_MB = 100
    UPLOAD_DIR = "./data/uploads"
    
    # 任务配置
    TASK_TIMEOUT = 3600  # 1小时
    TASK_RETRY_MAX = 3
    
    # 向量化配置
    CHUNK_SIZE = 1000
    CHUNK_OVERLAP = 200
    RETRIEVAL_TOP_K = 5
    
    # LLM配置
    LLM_MAX_TOKENS = 4000
    LLM_TEMPERATURE = 0.1
    
    # 缓存配置
    CACHE_TTL = 3600  # 1小时
    MAX_CACHE_SIZE = 1000


# 错误代码
class ErrorCodes:
    """错误代码常量"""
    
    # 通用错误
    INTERNAL_ERROR = "INTERNAL_ERROR"
    INVALID_REQUEST = "INVALID_REQUEST"
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    NOT_FOUND = "NOT_FOUND"
    
    # 文件相关错误
    FILE_NOT_FOUND = "FILE_NOT_FOUND"
    FILE_TOO_LARGE = "FILE_TOO_LARGE"
    INVALID_FILE_TYPE = "INVALID_FILE_TYPE"
    FILE_UPLOAD_FAILED = "FILE_UPLOAD_FAILED"
    
    # 任务相关错误
    TASK_NOT_FOUND = "TASK_NOT_FOUND"
    TASK_FAILED = "TASK_FAILED"
    TASK_TIMEOUT = "TASK_TIMEOUT"
    
    # 服务相关错误
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    SERVICE_TIMEOUT = "SERVICE_TIMEOUT"
    
    # LLM相关错误
    LLM_API_ERROR = "LLM_API_ERROR"
    LLM_RATE_LIMIT = "LLM_RATE_LIMIT"
    LLM_INVALID_RESPONSE = "LLM_INVALID_RESPONSE"


# 监控指标
class MetricNames:
    """监控指标名称"""
    
    # 请求指标
    REQUEST_COUNT = "http_requests_total"
    REQUEST_DURATION = "http_request_duration_seconds"
    
    # 任务指标
    TASK_COUNT = "celery_tasks_total"
    TASK_DURATION = "celery_task_duration_seconds"
    
    # 系统指标
    MEMORY_USAGE = "memory_usage_bytes"
    CPU_USAGE = "cpu_usage_percent"
    GPU_USAGE = "gpu_usage_percent"
    
    # 业务指标
    FILE_UPLOAD_COUNT = "file_uploads_total"
    VECTORIZATION_COUNT = "vectorizations_total"
    ANALYSIS_COUNT = "analyses_total"


# 版本信息
class Versions:
    """版本信息常量"""
    API_VERSION = "v1"
    SHARED_VERSION = "1.0.0"
    MIN_PYTHON_VERSION = "3.10"


# 导出所有常量
__all__ = [
    "ServiceNames",
    "ServicePorts", 
    "QueueNames",
    "TaskNames",
    "FileStatus",
    "TaskStatus",
    "AnalysisStatus",
    "RiskLevel",
    "LLMProvider",
    "DeviceType",
    "LogLevel",
    "Environment",
    "APIRoutes",
    "FileTypes",
    "ConfigDefaults",
    "ErrorCodes",
    "MetricNames",
    "Versions",
]
