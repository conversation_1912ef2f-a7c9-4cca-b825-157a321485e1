"""
Telemetry disable module
Programmatically disable telemetry for ChromaDB, PostHog, and other services
"""

import os
import logging

logger = logging.getLogger(__name__)

# All known telemetry environment variables
TELEMETRY_DISABLE_VARS = {
    # ChromaDB specific telemetry disable flags
    'ANONYMIZED_TELEMETRY': 'False',
    'CHROMA_ANONYMIZED_TELEMETRY': 'False', 
    'CHROMA_DISABLE_TELEMETRY': 'True',
    'CHROMA_TELEMETRY_DISABLED': 'True',
    
    # PostHog specific disable flags
    'POSTHOG_DISABLED': 'True',
    'POSTHOG_FEATURE_FLAGS_DISABLED': 'True',
    'DISABLE_POSTHOG': 'True',
    
    # General telemetry disable flags
    'DISABLE_TELEMETRY': '1',
    'NO_ANALYTICS': '1',
    'NO_TELEMETRY': '1',
    'TELEMETRY_DISABLED': '1',
    
    # <PERSON><PERSON><PERSON><PERSON> telemetry disable
    'LANGCHAIN_TRACING_V2': 'false',
    'LANGCHAIN_TRACING': 'false',
    'LANGCHAIN_ENDPOINT': '',
    'LANGCHAIN_API_KEY': '',
    
    # OpenTelemetry disable
    'OTEL_SDK_DISABLED': 'true',
    'OTEL_TRACES_EXPORTER': 'none',
    'OTEL_METRICS_EXPORTER': 'none',
    'OTEL_LOGS_EXPORTER': 'none',
}


def disable_telemetry():
    """
    Disable telemetry by setting environment variables
    """
    logger.info("Disabling telemetry for all services...")
    
    set_count = 0
    already_set_count = 0
    
    for var, value in TELEMETRY_DISABLE_VARS.items():
        if var in os.environ:
            current_value = os.environ[var]
            if current_value != value:
                logger.debug(f"Updating {var}: {current_value} -> {value}")
                os.environ[var] = value
                set_count += 1
            else:
                logger.debug(f"Already set {var}={value}")
                already_set_count += 1
        else:
            logger.debug(f"Setting {var}={value}")
            os.environ[var] = value
            set_count += 1
    
    logger.info(f"Telemetry disabled: {set_count} variables set, {already_set_count} already configured")
    
    return set_count > 0


def is_telemetry_disabled():
    """
    Check if telemetry is properly disabled
    """
    disabled_vars = []
    enabled_vars = []
    
    for var, expected_value in TELEMETRY_DISABLE_VARS.items():
        current_value = os.environ.get(var, '')
        
        if current_value == expected_value:
            disabled_vars.append(var)
        else:
            enabled_vars.append((var, current_value, expected_value))
    
    is_disabled = len(enabled_vars) == 0
    
    logger.debug(f"Telemetry check: {len(disabled_vars)} disabled, {len(enabled_vars)} not properly set")
    
    if enabled_vars:
        logger.warning("Some telemetry variables are not properly disabled:")
        for var, current, expected in enabled_vars:
            logger.warning(f"  {var}: current='{current}', expected='{expected}'")
    
    return is_disabled


def get_telemetry_status():
    """
    Get detailed telemetry status
    """
    status = {
        'disabled_vars': [],
        'enabled_vars': [],
        'missing_vars': [],
        'is_fully_disabled': True
    }
    
    for var, expected_value in TELEMETRY_DISABLE_VARS.items():
        current_value = os.environ.get(var)
        
        if current_value is None:
            status['missing_vars'].append(var)
            status['is_fully_disabled'] = False
        elif current_value == expected_value:
            status['disabled_vars'].append(var)
        else:
            status['enabled_vars'].append({
                'var': var,
                'current': current_value,
                'expected': expected_value
            })
            status['is_fully_disabled'] = False
    
    return status


# Auto-disable telemetry when module is imported
try:
    disable_telemetry()
except Exception as e:
    logger.error(f"Failed to disable telemetry: {e}")