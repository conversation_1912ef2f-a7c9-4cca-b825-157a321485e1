version: '3.8'

services:
  # Celery Worker
  worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: deep-risk-worker
    environment:
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - WORKER_TYPE=worker
      - WORKER_CONCURRENCY=${WORKER_CONCURRENCY:-4}
      
      # Redis配置
      - REDIS_URL=redis://redis:6379/0
      
      # 外部服务
      - CHROMA_URL=http://chromadb:8000
      - EMBEDDING_SERVICE_URL=http://embedding-service:8004
      
      # LLM配置
      - LLM_PROVIDER=${LLM_PROVIDER:-deepseek}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENABLE_STREAMING=${ENABLE_STREAMING:-true}
      
      # 任务配置
      - TASK_TIMEOUT=${TASK_TIMEOUT:-3600}
      - TASK_RETRY_MAX=${TASK_RETRY_MAX:-3}
      
      # 扩展配置
      - ENABLE_AUTO_SCALING=${ENABLE_AUTO_SCALING:-false}
      - MIN_WORKERS=${MIN_WORKERS:-1}
      - MAX_WORKERS=${MAX_WORKERS:-10}
    volumes:
      # 文件上传目录
      - ${UPLOAD_PATH:-./data/uploads}:/app/data/uploads
      # 日志目录
      - worker_logs:/app/logs
      # 缓存目录
      - worker_cache:/app/cache
      # 配置目录 - 挂载.env等配置文件（可选）
      - ${CONFIG_PATH:-./config}:/app/config:ro
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - worker-network

  # Flower监控
  flower:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: deep-risk-flower
    ports:
      - "${FLOWER_PORT:-5555}:5555"
    environment:
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - WORKER_TYPE=flower
      - REDIS_URL=redis://redis:6379/0
      - FLOWER_PORT=5555
    volumes:
      - flower_logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - worker-network
    profiles:
      - monitoring

  # Celery Beat调度器
  beat:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: deep-risk-beat
    environment:
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - WORKER_TYPE=beat
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - beat_logs:/app/logs
      - beat_schedule:/app/celerybeat-schedule
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - worker-network
    profiles:
      - scheduler

  # Redis - 消息队列
  redis:
    image: redis:7-alpine
    container_name: worker-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - worker-network

  # 可选：ChromaDB (如果需要本地运行)
  chromadb:
    image: chromadb/chroma:latest
    container_name: worker-chromadb
    ports:
      - "${CHROMA_PORT:-8001}:8000"
    volumes:
      - chromadb_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - worker-network
    profiles:
      - with-chromadb

  # 可选：嵌入服务 (如果需要本地运行)
  embedding-service:
    image: deep-risk-embedding:latest
    container_name: worker-embedding
    ports:
      - "${EMBEDDING_PORT:-8004}:8004"
    environment:
      - HOST=0.0.0.0
      - PORT=8004
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEVICE=${DEVICE:-auto}
    volumes:
      - ${MODEL_PATH:-../../models}:/app/models:ro
      - embedding_cache:/app/cache
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/api/v1/health/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - worker-network
    profiles:
      - with-embedding

  # 可选：Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: worker-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - worker-network
    profiles:
      - monitoring

  # 可选：Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: worker-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - worker-network
    profiles:
      - monitoring

volumes:
  redis_data:
    driver: local
  chromadb_data:
    driver: local
  worker_logs:
    driver: local
  worker_cache:
    driver: local
  flower_logs:
    driver: local
  beat_logs:
    driver: local
  beat_schedule:
    driver: local
  embedding_cache:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  worker-network:
    driver: bridge

# 使用说明:
# 1. 基本启动 (Worker + Redis): docker-compose up -d
# 2. 包含监控: docker-compose --profile monitoring up -d
# 3. 包含调度器: docker-compose --profile scheduler up -d
# 4. 包含ChromaDB: docker-compose --profile with-chromadb up -d
# 5. 包含嵌入服务: docker-compose --profile with-embedding up -d
# 6. 完整服务: docker-compose --profile monitoring --profile scheduler --profile with-chromadb --profile with-embedding up -d
# 7. 多Worker实例: docker-compose up -d --scale worker=3
