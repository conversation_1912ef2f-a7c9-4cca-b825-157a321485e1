#!/bin/bash

# Worker服务健康检查脚本

set -e

# 检查Worker类型
WORKER_TYPE=${WORKER_TYPE:-worker}

case "$WORKER_TYPE" in
    worker)
        # 检查Celery Worker进程
        if pgrep -f "celery.*worker" > /dev/null; then
            echo "✅ Celery Worker is running"
            exit 0
        else
            echo "❌ Celery Worker is not running"
            exit 1
        fi
        ;;
    flower)
        # 检查Flower进程和HTTP端口
        if pgrep -f "celery.*flower" > /dev/null; then
            if curl -f -s http://localhost:${FLOWER_PORT:-5555}/api/workers > /dev/null; then
                echo "✅ Flower is running and accessible"
                exit 0
            else
                echo "❌ Flower is running but not accessible"
                exit 1
            fi
        else
            echo "❌ Flower is not running"
            exit 1
        fi
        ;;
    beat)
        # Beat调度器已禁用
        echo "⚠️ Celery Beat is disabled"
        exit 0
        ;;
    *)
        echo "❌ Unknown worker type: $WORKER_TYPE"
        exit 1
        ;;
esac
