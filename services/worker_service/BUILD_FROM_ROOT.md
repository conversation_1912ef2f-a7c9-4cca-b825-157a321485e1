# Worker 服务依赖修复方案

## 问题说明

Worker 服务严重依赖 shared 组件，包含：
- shared.utils.logger (日志系统)
- shared.celery_config (Celery配置)
- shared.constants (常量定义)
- shared.models (数据模型)
- shared.llm (LLM客户端)
- 等 15+ 个模块

**如果没有 shared 组件，Worker 服务无法正常启动**

## 解决方案：从项目根目录构建

### 1. 构建命令

```bash
# 从项目根目录构建
cd /path/to/deep-risk-rag
docker build -f services/worker_service/Dockerfile -t worker-service .

# 而不是从 worker_service 目录构建
cd services/worker_service  # ❌ 错误方式
docker build -t worker-service .
```

### 2. Dockerfile 已优化

```dockerfile
# 复制共享组件
COPY shared ./shared

# 复制Worker服务代码
COPY services/worker_service/ .
```

### 3. .dockerignore 配置

项目根目录需要创建或更新 `.dockerignore`:

```bash
# 项目根目录的 .dockerignore
.git
__pycache__
*.pyc
*.log
.env*
!.env.example

# 排除其他服务
services/deep_service
services/embedding_service  
# 但保留 shared 和 worker_service
```

### 4. Docker Compose 配置

```yaml
services:
  worker:
    build:
      context: .  # 项目根目录
      dockerfile: services/worker_service/Dockerfile
    # ... 其他配置
```

## 验证步骤

### 1. 构建验证
```bash
cd /path/to/deep-risk-rag
docker build -f services/worker_service/Dockerfile -t worker-service .
```

### 2. 依赖验证
```bash
docker run --rm worker-service python -c "
from shared.utils.logger import get_logger
from shared.celery_config import celery_app
from shared.constants import ServiceNames
print('✅ 所有依赖正常')
"
```

### 3. 服务验证
```bash
docker run --rm worker-service ./start.sh --check
```

## 目录结构要求

```
deep-risk-rag/
├── shared/                 # ✅ 必须存在
│   ├── utils/
│   ├── models/
│   ├── celery_config.py
│   └── constants.py
├── services/
│   └── worker_service/     # ✅ 必须存在
│       ├── Dockerfile
│       ├── worker.py
│       ├── core/
│       └── tasks/
└── .dockerignore          # ✅ 推荐配置
```

## 常见错误

### ❌ 构建上下文错误
```bash
cd services/worker_service
docker build -t worker-service .
# 错误：无法找到 shared 组件
```

### ❌ 路径错误
```dockerfile
COPY ../shared ./shared  # 错误：路径超出构建上下文
```

### ✅ 正确方式
```bash
cd deep-risk-rag  # 项目根目录
docker build -f services/worker_service/Dockerfile -t worker-service .
```

## 生产环境部署

### Docker Compose
```yaml
version: '3.8'
services:
  worker:
    build:
      context: .
      dockerfile: services/worker_service/Dockerfile
```

### Kubernetes
```yaml
# 确保构建上下文包含整个项目
docker build -f services/worker_service/Dockerfile -t registry/worker-service .
docker push registry/worker-service
```

## 独立部署选项（高级）

如果需要完全独立的 Worker 服务：

1. **将 shared 代码内嵌**：复制必要的 shared 文件到 worker_service
2. **创建 worker_service/shared/**：本地化依赖
3. **修改导入路径**：from shared -> from .shared

但推荐保持当前共享架构，通过正确的构建方式解决依赖问题。 