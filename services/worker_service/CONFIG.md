# Worker 服务配置指南

## 配置安全优化

为了提高安全性和灵活性，Worker 服务现在支持多种配置方式，避免将敏感信息打包到 Docker 镜像中。

## 配置方式

### 1. 环境变量（推荐）

通过 Docker 环境变量传递配置：

```bash
docker run -e DEEPSEEK_API_KEY=your_key -e OPENAI_API_KEY=your_key worker-service
```

### 2. 配置文件挂载（推荐）

创建外部配置目录并挂载：

```bash
# 创建配置目录
mkdir -p ./config

# 复制配置模板
cp .env.example ./config/.env

# 编辑配置文件
nano ./config/.env

# 启动容器时挂载配置
docker run -v ./config:/app/config:ro worker-service
```

### 3. Docker Compose 配置

在 `docker-compose.yml` 中配置：

```yaml
services:
  worker:
    environment:
      # 直接设置环境变量
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      # 挂载配置目录
      - ${CONFIG_PATH:-./config}:/app/config:ro
```

## 配置优先级

配置加载优先级（从高到低）：

1. **环境变量**：Docker 运行时设置的环境变量
2. **挂载配置文件**：`/app/config/.env`
3. **本地配置文件**：`.env`（容器内，已被 .dockerignore 排除）
4. **默认值**：代码中定义的默认值

## 安全配置实践

### ✅ 推荐做法

1. **使用环境变量**：
   ```bash
   export DEEPSEEK_API_KEY="your_secret_key"
   export OPENAI_API_KEY="your_secret_key"
   docker-compose up -d
   ```

2. **使用配置文件挂载**：
   ```bash
   # 创建外部配置
   mkdir -p ./config
   echo "DEEPSEEK_API_KEY=your_secret_key" > ./config/.env
   echo "OPENAI_API_KEY=your_secret_key" >> ./config/.env
   
   # 挂载配置
   docker run -v ./config:/app/config:ro worker-service
   ```

3. **使用 Docker Secrets**（生产环境）：
   ```yaml
   secrets:
     deepseek_key:
       external: true
   services:
     worker:
       secrets:
         - deepseek_key
   ```

### ❌ 避免的做法

1. **不要将 .env 文件打包到镜像中**：
   - 现已通过 `.dockerignore` 自动排除
   
2. **不要在 Dockerfile 中硬编码密钥**：
   ```dockerfile
   # ❌ 错误示例
   ENV DEEPSEEK_API_KEY=sk-xxx
   ```

3. **不要在公共代码仓库中提交 .env 文件**：
   - 使用 `.env.example` 作为模板
   - 将 `.env` 添加到 `.gitignore`

## 配置模板

### .env.example 文件结构

```bash
# 服务基本配置
LOG_LEVEL=INFO
ENVIRONMENT=production

# 嵌入服务配置
EMBEDDING_SERVICE_URL=http://localhost:8004
EMBEDDING_SERVICE_TIMEOUT=300.0

# ChromaDB配置
CHROMA_HOST=localhost
CHROMA_PORT=8001

# LLM配置（敏感信息，请替换为实际值）
LLM_PROVIDER=deepseek
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_MODEL=deepseek-reasoner

# OpenAI兼容服务配置
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# 任务配置
TASK_TIMEOUT=1800
MAX_RETRIES=3
WORKER_CONCURRENCY=4
```

## 部署场景

### 开发环境

```bash
# 使用本地配置文件
cp .env.example .env
# 编辑 .env 文件
docker-compose up -d
```

### 测试环境

```bash
# 使用环境变量
export CONFIG_ENV=testing
export DEEPSEEK_API_KEY="test_key"
docker-compose --profile testing up -d
```

### 生产环境

```bash
# 使用外部配置挂载
mkdir -p /etc/worker-service
cp .env.example /etc/worker-service/.env
# 编辑生产配置
nano /etc/worker-service/.env

# 启动时挂载
docker run -v /etc/worker-service:/app/config:ro worker-service
```

## 验证配置

### 检查配置加载

```bash
# 检查容器中的配置
docker exec -it worker-container python -c "
from config import config
print(f'LLM Provider: {config.llm_provider}')
print(f'Embedding URL: {config.embedding_service_url}')
print(f'ChromaDB: {config.chroma_url}')
"
```

### 健康检查

```bash
# 检查服务健康状态
docker exec worker-container ./start.sh --check
```

## 故障排除

### 配置未生效

1. 检查挂载路径：
   ```bash
   docker exec worker-container ls -la /app/config/
   ```

2. 检查环境变量：
   ```bash
   docker exec worker-container env | grep -E "(DEEPSEEK|OPENAI|CHROMA)"
   ```

3. 检查配置加载顺序：
   ```bash
   docker exec worker-container python -c "
   from config import WorkerServiceConfig
   print(WorkerServiceConfig().Config.env_file)
   "
   ```

### 权限问题

确保配置文件权限正确：

```bash
# 设置配置文件权限
chmod 600 ./config/.env
chown $(id -u):$(id -g) ./config/.env
```

## 安全建议

1. **最小权限原则**：只挂载必要的配置文件
2. **只读挂载**：使用 `:ro` 标志进行只读挂载
3. **定期轮换**：定期更换 API 密钥和其他敏感信息
4. **监控访问**：监控配置文件的访问和修改
5. **备份配置**：安全备份重要的配置文件 