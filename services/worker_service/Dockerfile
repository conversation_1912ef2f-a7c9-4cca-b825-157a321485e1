# Worker服务独立 Dockerfile
# 后台任务处理服务

FROM python:3.10-slim AS base

# 设置标签和元数据
LABEL maintainer="Deep Risk RAG Team"
LABEL description="Worker Service for Deep Risk RAG - Background task processing"
LABEL version="1.0.0"
LABEL service="worker-service"

# 设置工作目录
WORKDIR /app

# 安装系统依赖，优化缓存和大小
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    && pip install --upgrade pip \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建非root用户和组
RUN groupadd -r worker && useradd -r -g worker worker

# 复制requirements文件 (相对于项目根目录)
COPY services/worker_service/requirements.txt .

# 安装Python依赖，优化构建缓存
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt

# 复制共享组件 (相对于项目根目录)
COPY shared/ ./shared/

# 复制Worker服务代码到正确的目录结构 (相对于项目根目录)
COPY services/worker_service/ ./services/worker_service/

# 创建必要目录并设置权限
RUN mkdir -p /app/data/uploads /app/logs /app/cache /app/pids /app/config && \
    chown -R worker:worker /app && \
    chmod -R 755 /app && \
    chmod +x /app/services/worker_service/healthcheck.sh # Make sure healthcheck is executable

# 设置环境变量
ENV PYTHONPATH=/app
ENV LOG_FILE=/app/logs/worker_service.log
ENV UPLOAD_DIR=/app/data/uploads
ENV WORKER_TYPE=worker

# 声明可挂载的数据卷
# 日志目录 - 便于外部访问和持久化日志
VOLUME ["/app/logs"]
# 缓存目录 - 便于缓存数据持久化
VOLUME ["/app/cache"]
# 上传文件目录 - 便于文件数据持久化
VOLUME ["/app/data/uploads"]
# 配置目录 - 便于外部挂载配置文件（如.env）
VOLUME ["/app/config"]

# 暴露端口
# 5555: Flower监控界面端口（当WORKER_TYPE=flower时使用）
EXPOSE 5555

# 切换到非root用户
USER worker


# 健康检查
# 根据WORKER_TYPE环境变量进行不同的健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD /app/services/worker_service/healthcheck.sh

# 默认启动命令 
# 可通过环境变量WORKER_TYPE覆盖启动模式：
# - worker: 启动Celery Worker
# - flower: 启动Flower监控
# - beat: 启动Celery Beat调度器（已禁用）
CMD ["./services/worker_service/start.sh", "--docker"]
