#!/bin/bash

# Worker服务独立启动脚本
# 后台任务处理服务启动脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# 显示帮助信息
show_help() {
    echo "Worker服务启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
echo "  -h, --help        显示帮助信息"
echo "  -w, --worker      启动Celery Worker (前台)"
echo "  -f, --flower      启动Flower监控 (前台)"
echo "  -a, --all         启动所有服务 (后台)"
echo "  --foreground      强制前台启动 (与 -a 配合使用)"
echo "  --check           检查环境和依赖"
echo "  --install         安装依赖"
echo "  --test            运行测试"
echo "  --docker          Docker模式启动"
    echo ""
    echo "环境变量:"
    echo "  LOG_LEVEL         日志级别 (默认: INFO)"
    echo "  WORKER_CONCURRENCY Worker并发数 (默认: 4)"
    echo "  FLOWER_PORT       Flower端口 (默认: 5555)"
    echo ""
    echo "配置说明:"
echo "  其他配置项(Redis、ChromaDB、嵌入服务等)由应用配置文件管理"
echo ""
echo "启动模式说明:"
echo "  单独启动 (-w/-f): 默认前台运行，便于调试和查看日志"
echo "  全部启动 (-a):   默认后台运行，适合生产环境"
echo "  前台强制 (--foreground): 与 -a 配合使用，强制前台运行"
echo ""
echo "示例:"
echo "  $0 -w                     前台启动Worker"
echo "  $0 -f                     前台启动Flower监控"
echo "  $0 -a                     后台启动所有服务"
echo "  $0 -a --foreground        前台启动所有服务"
echo "  $0 --foreground -a        前台启动所有服务"
echo ""
echo "隐私保护:"
echo "  自动禁用所有遥测数据发送，包括 PostHog、LangChain 等服务"
echo ""
}

# 检查Python环境
check_python() {
    print_header "🐍 检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3未安装"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    print_message "Python版本: $python_version"
    
    # 检查Python版本是否满足要求
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 10) else 1)"; then
        print_error "需要Python 3.10或更高版本"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    print_header "📦 检查依赖..."
    
    # 检查requirements.txt
    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt文件不存在"
        exit 1
    fi
    
    # 检查关键依赖
    python3 -c "
import sys
try:
    import celery
    import redis
    import httpx
    import pandas
    import chromadb
    print('✅ 关键依赖检查通过')
except ImportError as e:
    print(f'❌ 缺少依赖: {e}')
    sys.exit(1)
"
}

# 检查外部服务
check_external_services() {
    print_header "🔗 检查外部服务..."
    
    # 检查Redis
    redis_host=${REDIS_HOST:-localhost}
    redis_port=${REDIS_PORT:-6379}
    
    if command -v redis-cli &> /dev/null; then
        if redis-cli -h $redis_host -p $redis_port ping &> /dev/null; then
            print_message "Redis连接正常: $redis_host:$redis_port"
        else
            print_warning "Redis连接失败: $redis_host:$redis_port"
        fi
    else
        print_warning "redis-cli未安装，无法检查Redis连接"
    fi
    
    # 检查ChromaDB
    chroma_host=${CHROMA_HOST:-localhost}
    chroma_port=${CHROMA_PORT:-8001}
    
    if command -v curl &> /dev/null; then
        if curl -f -s "http://$chroma_host:$chroma_port/api/v1/heartbeat" &> /dev/null; then
            print_message "ChromaDB连接正常: $chroma_host:$chroma_port"
        else
            print_warning "ChromaDB连接失败: $chroma_host:$chroma_port"
        fi
    fi
    
    # 检查嵌入服务
    embedding_url=${EMBEDDING_SERVICE_URL:-http://localhost:8004}
    
    if command -v curl &> /dev/null; then
        if curl -f -s "$embedding_url/api/v1/health/ready" &> /dev/null; then
            print_message "嵌入服务连接正常: $embedding_url"
        else
            print_warning "嵌入服务连接失败: $embedding_url"
        fi
    fi
}

# 检查配置
check_configuration() {
    print_header "⚙️ 检查配置..."
    
    print_message "配置检查：应用配置由配置文件管理"
}

# 安装依赖
install_dependencies() {
    print_header "📦 安装依赖..."
    
    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt文件不存在"
        exit 1
    fi
    
    print_message "安装Python依赖..."
    pip3 install -r requirements.txt
    
    print_message "依赖安装完成"
}

# 运行测试
run_tests() {
    print_header "🧪 运行测试..."
    
    if command -v pytest &> /dev/null; then
        pytest tests/ -v
    else
        print_warning "pytest未安装，跳过测试"
    fi
}

# 设置环境变量
setup_environment() {
    print_header "⚙️ 设置环境..."
    
    # 设置日志级别
    export LOG_LEVEL=${LOG_LEVEL:-"INFO"}
    
    # 完全禁用所有遥测数据发送，保护隐私和数据安全
    export POSTHOG_HOST=""
    export POSTHOG_PROJECT_ID=""
    export POSTHOG_API_KEY=""
    export POSTHOG_FEATURE_FLAGS="false"
    
    # 禁用LangChain遥测和分析
    export LANGCHAIN_TRACING_V2="false"
    export LANGCHAIN_ANALYTICS="false"
    export LANGCHAIN_TRACING="false"
    export LANGCHAIN_TRACKING="false"
    export LANGCHAIN_ENDPOINT=""
    export LANGCHAIN_API_KEY=""
    
    # 禁用其他可能的遥测
    export TELEMETRY_DISABLED="true"
    export DO_NOT_TRACK="1"
    export ANALYTICS_DISABLED="true"
    
    print_message "日志级别: $LOG_LEVEL"
    print_message "✅ 遥测数据发送已禁用"
}

# 启动Celery Worker
start_worker() {
    print_header "🔧 启动Celery Worker..."
    
    local concurrency=${WORKER_CONCURRENCY:-4}
    local loglevel=${LOG_LEVEL:-INFO}
    local run_in_background=${1:-false}  # 通过参数控制
    
    print_message "启动Worker，并发数: $concurrency"
    
    if [ "$run_in_background" = "true" ]; then
        print_message "在后台启动Worker..."
        nohup celery -A worker worker \
            --loglevel=$loglevel \
            --concurrency=$concurrency \
            --prefetch-multiplier=1 \
            --max-tasks-per-child=1000 \
            > logs/worker.log 2>&1 &
        echo $! > pids/worker.pid
        print_message "Worker PID: $(cat pids/worker.pid)"
        print_message "日志文件: logs/worker.log"
    else
        print_message "前台启动Worker (Ctrl+C 停止)..."
        celery -A worker worker \
            --loglevel=$loglevel \
            --concurrency=$concurrency \
            --prefetch-multiplier=1 \
            --max-tasks-per-child=1000
    fi
}

# 启动Flower监控
start_flower() {
    print_header "🌸 启动Flower监控..."
    
    # 检查flower是否安装
    if ! python3 -c "import flower" &> /dev/null; then
        print_error "Flower未安装，请先安装: pip install flower"
        print_warning "或者取消注释 requirements.txt 中的 flower>=2.0.0"
        exit 1
    fi
    
    local port=${FLOWER_PORT:-5555}
    local run_in_background=${1:-false}  # 通过参数控制
    
    print_message "启动Flower监控，端口: $port"
    
    if [ "$run_in_background" = "true" ]; then
        print_message "在后台启动Flower..."
        nohup celery -A worker flower --port=$port \
            > logs/flower.log 2>&1 &
        echo $! > pids/flower.pid
        print_message "Flower PID: $(cat pids/flower.pid)"
        print_message "日志文件: logs/flower.log"
        print_message "监控地址: http://localhost:$port"
    else
        print_message "前台启动Flower (Ctrl+C 停止)..."
        print_message "监控地址: http://localhost:$port"
        celery -A worker flower --port=$port
    fi
}

# 启动Celery Beat（已禁用）
# start_beat() {
#     print_header "⏰ 启动Celery Beat..."
#     
#     local run_in_background=${BEAT_BACKGROUND:-false}
#     
#     print_message "启动Beat调度器..."
#     
#     if [ "$run_in_background" = "true" ]; then
#         print_message "在后台启动Beat..."
#         nohup celery -A worker beat --loglevel=info \
#             > logs/beat.log 2>&1 &
#         echo $! > pids/beat.pid
#         print_message "Beat PID: $(cat pids/beat.pid)"
#         print_message "日志文件: logs/beat.log"
#     else
#         celery -A worker beat --loglevel=info
#     fi
# }

# 启动所有服务
start_all() {
    local run_in_foreground=${1:-false}  # 通过参数控制是否前台运行
    
    print_header "🚀 启动所有Worker服务..."
    
    if [ "$run_in_foreground" = "true" ]; then
        print_message "前台模式启动所有服务..."
        print_warning "注意：前台模式下，只能启动一个服务，建议分别启动"
        print_message "启动Worker (前台)..."
        start_worker false
    else
        print_message "后台模式启动所有服务..."
        
        # 在后台启动Worker
        start_worker true
        sleep 2  # 等待Worker启动
        
        # 在后台启动Flower (如果可用)
        if python3 -c "import flower" &> /dev/null; then
            start_flower true
        else
            print_warning "Flower未安装，跳过启动"
        fi
        
        print_message "所有Worker服务已启动"
        print_message "使用 './stop.sh --status' 查看服务状态"
        print_message "使用 './stop.sh --all' 停止所有服务"
    fi
}

# Docker模式启动
start_docker() {
    print_header "🐳 Docker模式启动..."
    
    # 在Docker环境中设置Python路径
    export PYTHONPATH="/app:$PYTHONPATH"
    
    case "${WORKER_TYPE:-worker}" in
        worker)
            print_message "启动Worker..."
            cd /app && exec python -m celery -A services.worker_service.worker worker --loglevel=info --concurrency=4
            ;;
        flower)
            print_message "启动Flower..."
            cd /app && exec python -m celery -A services.worker_service.worker flower --port=5555
            ;;
        beat)
            print_error "Beat调度器已禁用，如需使用请联系管理员"
            exit 1
            ;;
        *)
            print_error "未知的Worker类型: $WORKER_TYPE"
            exit 1
            ;;
    esac
}

# 主函数
main() {
    # 切换到脚本目录
    cd "$(dirname "$0")"
    
    # 创建必要目录
    mkdir -p logs pids
    
    # 检查是否有前台运行参数
    local run_foreground=false
    if [[ "$*" == *"--foreground"* ]]; then
        run_foreground=true
    fi
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        --check)
            check_python
            check_dependencies
            check_external_services
            check_configuration
            print_message "环境检查完成"
            ;;
        --install)
            check_python
            install_dependencies
            ;;
        --test)
            check_dependencies
            run_tests
            ;;
        -w|--worker)
            setup_environment
            check_dependencies
            check_configuration
            start_worker
            ;;
        -f|--flower)
            setup_environment
            check_dependencies
            start_flower
            ;;
        -b|--beat)
            print_error "Beat调度器已禁用，如需使用请联系管理员"
            exit 1
            ;;
        -a|--all)
            setup_environment
            check_dependencies
            check_configuration
            start_all "$run_foreground"
            ;;
        --foreground)
            # 单独的 --foreground 参数，默认启动所有服务
            if [ "$2" = "-a" ] || [ "$2" = "--all" ]; then
                setup_environment
                check_dependencies
                check_configuration
                start_all true
            else
                print_error "--foreground 参数需要与 -a/--all 配合使用"
                print_message "示例: $0 -a --foreground 或 $0 --foreground -a"
                exit 1
            fi
            ;;
        --docker)
            setup_environment
            start_docker
            ;;
        *)
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
