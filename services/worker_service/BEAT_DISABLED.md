# Celery Beat 调度器禁用说明

## 状态
Celery Beat 调度器已于 2025-07-02 被禁用。

## 原因
- 当前系统暂时不需要定时任务调度功能
- 避免不必要的资源消耗
- Beat 调度器启动后没有定时任务可执行，一直处于等待状态

## 修改内容

### 1. 启动脚本 (start.sh)
- 注释了 `start_beat()` 函数
- 移除了 `-b/--beat` 选项
- 在 Docker 模式中禁用 beat 类型
- 修改 `start_all()` 函数，不再启动 Beat
- **新增功能**：移除环境变量控制，改为参数控制前台/后台启动
  - 单独启动 (`-w/-f`): 默认前台运行
  - 全部启动 (`-a`): 默认后台运行
  - 添加 `--foreground` 参数强制前台运行

### 2. 停止脚本 (stop.sh)
- 从服务列表中移除 beat
- 修改状态检查，不再检查 Beat 进程
- 添加对 beat 参数的错误提示

### 3. 健康检查脚本 (healthcheck.sh)
- 修改 beat 类型检查，返回禁用状态而非错误

### 4. 清理文件
- 删除了 `celerybeat-schedule.db`
- 删除了 `pids/beat.pid`

## 重新启用方法

如果将来需要重新启用 Beat 调度器：

### 1. 恢复启动脚本
```bash
# 取消注释 start_beat() 函数
# 恢复 -b/--beat 选项
# 恢复 Docker 模式中的 beat 类型
# 修改 start_all() 函数重新包含 Beat
```

### 2. 恢复停止脚本
```bash
# 在服务列表中重新添加 beat
# 恢复状态检查中的 Beat 进程检查
# 移除对 beat 参数的错误提示
```

### 3. 恢复健康检查脚本
```bash
# 恢复原始的 Beat 进程检查逻辑
```

### 4. 配置定时任务
在 `shared/celery_config.py` 中添加 `beat_schedule` 配置：

```python
CELERY_CONFIG = {
    # ... 其他配置 ...
    
    # Beat 调度配置
    'beat_schedule': {
        'health-check': {
            'task': 'worker.tasks.health_check',
            'schedule': 300.0,  # 每5分钟执行一次
        },
        'cleanup-expired-tasks': {
            'task': 'worker.tasks.cleanup_expired_tasks',
            'schedule': crontab(hour=2, minute=0),  # 每天凌晨2点执行
        },
    },
    
    # ... 其他配置 ...
}
```

## 注意事项
- 重新启用时需要确保定义了合理的定时任务
- 注意检查所有相关的配置文件和脚本
- 测试 Beat 调度器的启动和停止功能 