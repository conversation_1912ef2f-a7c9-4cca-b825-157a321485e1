#!/bin/bash

# Worker服务停止脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# 停止单个服务
stop_service() {
    local service_name=$1
    local pid_file="pids/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            print_message "停止 ${service_name} (PID: $pid)..."
            kill -TERM "$pid"
            
            # 等待进程退出
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 30 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            if kill -0 "$pid" 2>/dev/null; then
                print_warning "${service_name} 未正常退出，强制终止..."
                kill -KILL "$pid"
            fi
            
            rm -f "$pid_file"
            print_message "${service_name} 已停止"
        else
            print_warning "${service_name} 进程不存在，清理PID文件"
            rm -f "$pid_file"
        fi
    else
        print_warning "${service_name} PID文件不存在"
    fi
}

# 停止所有服务
stop_all() {
    print_header "🛑 停止所有Worker服务..."
    
    stop_service "worker"
    stop_service "flower"
    # Beat调度器已禁用
    # stop_service "beat"
    
    print_message "所有服务已停止"
}

# 查看服务状态
show_status() {
    print_header "📊 Worker服务状态..."
    
    for service in worker flower; do  # beat已禁用
        local pid_file="pids/${service}.pid"
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if kill -0 "$pid" 2>/dev/null; then
                print_message "${service}: 运行中 (PID: $pid)"
            else
                print_warning "${service}: PID文件存在但进程不存在"
            fi
        else
            print_warning "${service}: 未运行"
        fi
    done
}

# 查看日志
show_logs() {
    local service=$1
    local log_file="logs/${service}.log"
    
    if [ -f "$log_file" ]; then
        print_header "📄 ${service} 日志 (最后50行)..."
        tail -n 50 "$log_file"
    else
        print_warning "${service} 日志文件不存在"
    fi
}

# 清理日志和PID文件
cleanup() {
    print_header "🧹 清理日志和PID文件..."
    
    # 清理PID文件
    rm -f pids/*.pid
    print_message "PID文件已清理"
    
    # 备份并清理日志
    if [ -d "logs" ] && [ "$(ls -A logs 2>/dev/null)" ]; then
        local backup_dir="logs/backup/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        mv logs/*.log "$backup_dir/" 2>/dev/null || true
        print_message "日志文件已备份到: $backup_dir"
    fi
    
    print_message "清理完成"
}

# 显示帮助信息
show_help() {
    echo "Worker服务停止脚本"
    echo ""
    echo "用法: $0 [选项] [服务名]"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  -a, --all         停止所有服务"
    echo "  -s, --status      查看服务状态"
    echo "  -l, --logs        查看日志"
    echo "  -c, --cleanup     清理日志和PID文件"
    echo ""
    echo "服务名:"
    echo "  worker            Worker进程"
    echo "  flower            Flower监控"
    # echo "  beat              Beat调度器"  # 已禁用
    echo ""
    echo "示例:"
    echo "  $0 --all              停止所有服务"
    echo "  $0 worker             停止Worker服务"
    echo "  $0 --logs worker      查看Worker日志"
    echo "  $0 --status           查看所有服务状态"
}

# 主函数
main() {
    # 切换到脚本目录
    cd "$(dirname "$0")"
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -a|--all)
            stop_all
            ;;
        -s|--status)
            show_status
            ;;
        -l|--logs)
            if [ -n "$2" ]; then
                show_logs "$2"
            else
                print_error "请指定服务名"
                exit 1
            fi
            ;;
        -c|--cleanup)
            cleanup
            ;;
        worker|flower)
            stop_service "$1"
            ;;
        beat)
            print_error "Beat调度器已禁用，无需停止"
            ;;
        "")
            show_help
            exit 1
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"