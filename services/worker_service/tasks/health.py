"""
健康检查任务
用于监控Worker服务的健康状态
"""

import time
import psutil
from datetime import datetime
from typing import Dict, Any
import asyncio

from shared.celery_config import celery_app
from shared.utils.logger import get_logger
from services.worker_service.config import config
from services.worker_service.core.analyzer import RiskAnalyzer
from services.worker_service.core.vectorizer import VectorizationProcessor

logger = get_logger(__name__)


@celery_app.task(bind=True, name="worker.tasks.health_check")
def health_check(self):
    """
    Worker健康检查任务

    Returns:
        健康检查结果
    """
    start_time = time.time()

    try:
        logger.info(f"开始Worker健康检查 (任务ID: {self.request.id})")

        # 更新任务进度
        self.update_state(
            state="STARTED",
            meta={
                "stage": "健康检查",
                "progress": 0,
                "started_at": datetime.now().isoformat(),
            },
        )

        health_status = {}

        # 1. 系统资源检查
        self.update_state(
            state="PROGRESS",
            meta={
                "stage": "系统资源检查",
                "progress": 20,
                "message": "检查CPU、内存、磁盘使用情况",
            },
        )

        health_status["system"] = _check_system_resources()

        # 2. 嵌入服务连接检查
        self.update_state(
            state="PROGRESS",
            meta={"stage": "嵌入服务检查", "progress": 40, "message": "检查嵌入服务连接"},
        )

        health_status["embedding_service"] = _check_embedding_service()

        # 3. 向量化组件检查
        self.update_state(
            state="PROGRESS",
            meta={
                "stage": "向量化组件检查",
                "progress": 60,
                "message": "检查BGE-M3模型和ChromaDB连接",
            },
        )

        health_status["vectorization"] = asyncio.run(_check_vectorization_components())

        # 4. 分析组件检查
        self.update_state(
            state="PROGRESS",
            meta={
                "stage": "分析组件检查",
                "progress": 80,
                "message": "检查LLM服务和分析功能",
            },
        )

        health_status["analysis"] = asyncio.run(_check_analysis_components())

        # 5. 整体健康状态评估
        overall_healthy = all(
            [
                health_status["system"]["healthy"],
                health_status["embedding_service"]["healthy"],
                health_status["vectorization"]["healthy"],
                health_status["analysis"]["healthy"],
            ]
        )

        total_time = time.time() - start_time

        # 完成
        self.update_state(
            state="SUCCESS",
            meta={
                "stage": "完成",
                "progress": 100,
                "overall_healthy": overall_healthy,
                "total_time": total_time,
                "completed_at": datetime.now().isoformat(),
            },
        )

        logger.info(
            f"Worker健康检查完成: {'健康' if overall_healthy else '异常'}, 耗时 {total_time:.2f}s"
        )

        return {
            "success": True,
            "overall_healthy": overall_healthy,
            "health_status": health_status,
            "check_time": total_time,
            "timestamp": datetime.now().isoformat(),
            "message": "Worker健康检查完成",
        }

    except Exception as e:
        error_msg = str(e)
        total_time = time.time() - start_time

        logger.error(f"Worker健康检查失败 (任务ID: {self.request.id}): {error_msg}")

        # 更新任务状态为失败
        self.update_state(
            state="FAILURE",
            meta={
                "stage": "失败",
                "error": error_msg,
                "total_time": total_time,
                "failed_at": datetime.now().isoformat(),
            },
        )

        raise


def _check_system_resources() -> Dict[str, Any]:
    """检查系统资源"""
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)

        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_available_gb = memory.available / (1024**3)

        # 磁盘使用情况
        disk = psutil.disk_usage("/")
        disk_percent = (disk.used / disk.total) * 100
        disk_free_gb = disk.free / (1024**3)

        # 健康状态判断
        healthy = (
            cpu_percent < 90
            and memory_percent < 90
            and disk_percent < 90
            and memory_available_gb > 1.0  # 至少1GB可用内存
            and disk_free_gb > 5.0  # 至少5GB可用磁盘
        )

        return {
            "healthy": healthy,
            "cpu_percent": cpu_percent,
            "memory_percent": memory_percent,
            "memory_available_gb": round(memory_available_gb, 2),
            "disk_percent": round(disk_percent, 2),
            "disk_free_gb": round(disk_free_gb, 2),
            "warnings": [],
        }

    except Exception as e:
        logger.error(f"系统资源检查失败: {e}")
        return {"healthy": False, "error": str(e)}


def _check_embedding_service() -> Dict[str, Any]:
    """检查嵌入服务连接状态"""
    try:
        from services.worker_service.core.embedding_client import SyncEmbeddingServiceClient

        # 创建嵌入服务客户端
        client = SyncEmbeddingServiceClient(
            base_url=config.embedding_service_url,
            timeout=5.0,  # 健康检查使用较短超时
            max_retries=1,
        )

        # 执行健康检查
        is_healthy = client.health_check()

        if is_healthy:
            return {
                "healthy": True,
                "available": True,
                "service_url": config.embedding_service_url,
                "message": "嵌入服务连接正常",
            }
        else:
            return {
                "healthy": False,
                "available": False,
                "service_url": config.embedding_service_url,
                "message": "嵌入服务健康检查失败",
            }

    except Exception as e:
        logger.error(f"嵌入服务连接检查失败: {e}")
        return {
            "healthy": False,
            "available": False,
            "error": str(e),
            "message": "无法连接到嵌入服务",
        }


async def _check_vectorization_components() -> Dict[str, Any]:
    """检查向量化组件 - 使用环境感知健康检查"""
    try:
        processor = VectorizationProcessor()

        # 使用处理器的环境感知健康检查
        logger.info(f"检查向量化组件 (环境: {processor._get_environment_name()})")
        health_result = processor.health_check()

        # 清理资源
        processor.cleanup()

        # 转换结果格式以保持兼容性
        return {
            "healthy": health_result.get("healthy", False),
            "embedding_service": health_result.get("embedding_service", False),
            "chromadb": health_result.get("chromadb", False),
            "environment": health_result.get("environment", "unknown"),
            "note": health_result.get("note", ""),
            "warnings": health_result.get("warnings", []),
            "error": health_result.get("error", None),
        }

    except Exception as e:
        logger.error(f"向量化组件检查失败: {e}")
        return {"healthy": False, "error": str(e), "environment": "error"}


async def _check_analysis_components() -> Dict[str, Any]:
    """检查分析组件"""
    try:
        logger.info("开始检查分析组件...")
        analyzer = RiskAnalyzer()

        # 执行健康检查
        logger.info("执行分析器健康检查...")
        health_checks = await analyzer.health_check()
        logger.info(f"健康检查结果: {health_checks}")

        # 详细记录每个组件的状态
        for component, status in health_checks.items():
            if status:
                logger.info(f"✅ {component}: 健康")
            else:
                logger.warning(f"❌ {component}: 不健康")

        # 清理资源
        await analyzer.cleanup()

        # 计算整体健康状态
        healthy = all(health_checks.values())
        logger.info(f"分析组件整体健康状态: {healthy}")

        return {
            "healthy": healthy,
            "components": health_checks,
            "warnings": [
                f"{comp}不健康" for comp, status in health_checks.items() if not status
            ],
        }

    except Exception as e:
        logger.error(f"分析组件检查失败: {e}")
        import traceback

        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return {"healthy": False, "error": str(e), "traceback": traceback.format_exc()}
