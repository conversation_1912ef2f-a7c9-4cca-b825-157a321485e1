# Worker服务环境配置示例
# 后台任务处理服务配置

# =============================================================================
# 服务基本配置
# =============================================================================
# 服务名称
SERVICE_NAME=worker-service

# 服务版本
SERVICE_VERSION=1.0.0

# 日志级别
LOG_LEVEL=INFO

# 部署环境 (development/staging/production)
ENVIRONMENT=development

# 部署模式 (standalone/docker/k8s)
DEPLOYMENT_MODE=standalone

# =============================================================================
# Celery配置
# =============================================================================
# Redis连接配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379/0

# Celery Broker和Backend
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Worker配置
WORKER_CONCURRENCY=4
WORKER_PREFETCH_MULTIPLIER=1
WORKER_MAX_TASKS_PER_CHILD=1000

# 任务配置
TASK_TIMEOUT=3600
TASK_RETRY_MAX=3
TASK_RETRY_DELAY=60

# =============================================================================
# 外部服务配置
# =============================================================================
# ChromaDB配置
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_API_VERSION=v1
CHROMA_URL=http://localhost:8001

# 嵌入服务配置
EMBEDDING_SERVICE_HOST=localhost
EMBEDDING_SERVICE_PORT=8004
EMBEDDING_SERVICE_URL=http://localhost:8004
EMBEDDING_SERVICE_TIMEOUT=300.0
EMBEDDING_SERVICE_MAX_RETRIES=3

# =============================================================================
# LLM配置
# =============================================================================
# LLM提供商 (deepseek/openai/claude/gemini)
LLM_PROVIDER=deepseek

# API密钥
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# 模型配置
DEEPSEEK_MODEL=deepseek-chat
OPENAI_MODEL=gpt-3.5-turbo
LLM_MAX_TOKENS=4000
LLM_TEMPERATURE=0.1

# OpenAI兼容服务配置
OPENAI_BASE_URL=https://api.openai.com/v1

# 流式输出
ENABLE_STREAMING=true

# =============================================================================
# 向量化配置
# =============================================================================
# 文档分块配置
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_CHUNK_SIZE=8192

# 批处理配置
BATCH_SIZE=32
MAX_LENGTH=8192
NORMALIZE_EMBEDDINGS=true

# 向量化超时
VECTORIZATION_TIMEOUT=1800

# =============================================================================
# 分析配置
# =============================================================================
# 分析超时时间 (秒)
ANALYSIS_TIMEOUT=1800

# 最大并发分析数
MAX_CONCURRENT_ANALYSES=5

# 检索配置
RETRIEVAL_TOP_K=5
RETRIEVAL_SCORE_THRESHOLD=0.7

# =============================================================================
# 水平扩展配置
# =============================================================================
# 启用自动扩展
ENABLE_AUTO_SCALING=false

# Worker数量限制
MIN_WORKERS=1
MAX_WORKERS=10

# 扩展阈值
TARGET_CPU_PERCENT=70.0
TARGET_MEMORY_PERCENT=80.0
SCALE_UP_THRESHOLD=85.0
SCALE_DOWN_THRESHOLD=30.0

# 扩展冷却时间 (秒)
SCALE_COOLDOWN_SECONDS=300

# 健康检查间隔 (秒)
HEALTH_CHECK_INTERVAL=30

# =============================================================================
# 监控配置
# =============================================================================
# Flower监控
FLOWER_PORT=5555
FLOWER_BASIC_AUTH=admin:password

# 启用Prometheus指标
ENABLE_METRICS=false
METRICS_PORT=9090

# 性能监控
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_SAMPLE_RATE=0.1

# =============================================================================
# 缓存配置
# =============================================================================
# 启用缓存
ENABLE_CACHE=true

# 缓存TTL (秒)
CACHE_TTL=3600

# 最大缓存大小
MAX_CACHE_SIZE=1000

# 缓存键前缀
CACHE_KEY_PREFIX=worker_

# =============================================================================
# 文件处理配置
# =============================================================================
# 文件上传目录
UPLOAD_DIR=./data/uploads

# 支持的文件类型
ALLOWED_EXTENSIONS=.csv,.xlsx,.xls

# 文件处理超时
FILE_PROCESSING_TIMEOUT=600

# 临时文件清理间隔 (秒)
TEMP_FILE_CLEANUP_INTERVAL=3600

# =============================================================================
# 错误处理配置
# =============================================================================
# 错误重试配置
MAX_RETRIES=3
RETRY_BACKOFF=true
RETRY_BACKOFF_MAX=600

# 死信队列
ENABLE_DLQ=true
DLQ_TTL=86400

# 错误通知
ENABLE_ERROR_NOTIFICATIONS=false
ERROR_WEBHOOK_URL=

# =============================================================================
# 安全配置
# =============================================================================
# 任务签名验证
ENABLE_TASK_SIGNATURE=false
TASK_SECRET_KEY=your_secret_key_here

# 网络安全
ALLOWED_HOSTS=localhost,127.0.0.1

# =============================================================================
# 日志配置
# =============================================================================
# 日志格式 (json/text)
LOG_FORMAT=text

# 日志文件路径
LOG_FILE=./logs/worker_service.log

# 日志轮转配置
LOG_MAX_SIZE=100
LOG_BACKUP_COUNT=5

# 结构化日志
ENABLE_STRUCTURED_LOGGING=false

# =============================================================================
# Docker特定配置
# =============================================================================
# Worker类型 (worker/flower/beat)
WORKER_TYPE=worker

# 容器内路径
DOCKER_UPLOAD_PATH=/app/data/uploads
DOCKER_LOG_PATH=/app/logs
DOCKER_CACHE_PATH=/app/cache

# 容器资源限制
DOCKER_MEMORY_LIMIT=2G
DOCKER_CPU_LIMIT=2.0

# =============================================================================
# 开发配置
# =============================================================================
# 开发模式
DEV_MODE=false

# 调试配置
DEBUG_TASKS=false
DEBUG_PERFORMANCE=false

# 测试模式
TEST_MODE=false
TEST_REDIS_URL=redis://localhost:6379/15

# Mock服务
MOCK_EMBEDDING_SERVICE=false
MOCK_LLM_SERVICE=false
