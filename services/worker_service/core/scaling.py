"""
Worker服务水平扩展支持
提供动态扩展、负载均衡和故障恢复功能
"""

import os
import time
import psutil
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

# 尝试导入共享组件
try:
    from shared.utils.logger import get_logger
    SHARED_AVAILABLE = True
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    SHARED_AVAILABLE = False

logger = get_logger(__name__)


@dataclass
class WorkerMetrics:
    """Worker指标数据"""
    worker_id: str
    cpu_percent: float
    memory_percent: float
    active_tasks: int
    processed_tasks: int
    failed_tasks: int
    last_heartbeat: datetime
    status: str  # active, idle, overloaded, failed


@dataclass
class ScalingConfig:
    """扩展配置"""
    min_workers: int = 1
    max_workers: int = 10
    target_cpu_percent: float = 70.0
    target_memory_percent: float = 80.0
    scale_up_threshold: float = 85.0
    scale_down_threshold: float = 30.0
    scale_cooldown_seconds: int = 300
    health_check_interval: int = 30


class WorkerScaler:
    """Worker自动扩展器"""
    
    def __init__(self, config: ScalingConfig):
        self.config = config
        self.workers: Dict[str, WorkerMetrics] = {}
        self.last_scale_action = datetime.now()
        self.scaling_enabled = os.getenv("ENABLE_AUTO_SCALING", "false").lower() == "true"
        
    def register_worker(self, worker_id: str) -> None:
        """注册Worker"""
        self.workers[worker_id] = WorkerMetrics(
            worker_id=worker_id,
            cpu_percent=0.0,
            memory_percent=0.0,
            active_tasks=0,
            processed_tasks=0,
            failed_tasks=0,
            last_heartbeat=datetime.now(),
            status="active"
        )
        logger.info(f"Worker registered: {worker_id}")
    
    def update_worker_metrics(
        self, 
        worker_id: str, 
        cpu_percent: float,
        memory_percent: float,
        active_tasks: int,
        processed_tasks: int = None,
        failed_tasks: int = None
    ) -> None:
        """更新Worker指标"""
        if worker_id not in self.workers:
            self.register_worker(worker_id)
        
        worker = self.workers[worker_id]
        worker.cpu_percent = cpu_percent
        worker.memory_percent = memory_percent
        worker.active_tasks = active_tasks
        worker.last_heartbeat = datetime.now()
        
        if processed_tasks is not None:
            worker.processed_tasks = processed_tasks
        if failed_tasks is not None:
            worker.failed_tasks = failed_tasks
        
        # 更新状态
        worker.status = self._determine_worker_status(worker)
        
        logger.debug(f"Worker metrics updated: {worker_id} - CPU: {cpu_percent}%, Memory: {memory_percent}%, Tasks: {active_tasks}")
    
    def _determine_worker_status(self, worker: WorkerMetrics) -> str:
        """确定Worker状态"""
        # 检查心跳
        if datetime.now() - worker.last_heartbeat > timedelta(seconds=self.config.health_check_interval * 2):
            return "failed"
        
        # 检查负载
        if (worker.cpu_percent > self.config.scale_up_threshold or 
            worker.memory_percent > self.config.scale_up_threshold):
            return "overloaded"
        
        # 检查空闲状态
        if (worker.cpu_percent < self.config.scale_down_threshold and 
            worker.memory_percent < self.config.scale_down_threshold and 
            worker.active_tasks == 0):
            return "idle"
        
        return "active"
    
    def get_cluster_metrics(self) -> Dict[str, any]:
        """获取集群指标"""
        active_workers = [w for w in self.workers.values() if w.status in ["active", "overloaded"]]
        
        if not active_workers:
            return {
                "total_workers": 0,
                "active_workers": 0,
                "avg_cpu_percent": 0.0,
                "avg_memory_percent": 0.0,
                "total_active_tasks": 0,
                "total_processed_tasks": 0,
                "total_failed_tasks": 0,
            }
        
        return {
            "total_workers": len(self.workers),
            "active_workers": len(active_workers),
            "avg_cpu_percent": sum(w.cpu_percent for w in active_workers) / len(active_workers),
            "avg_memory_percent": sum(w.memory_percent for w in active_workers) / len(active_workers),
            "total_active_tasks": sum(w.active_tasks for w in active_workers),
            "total_processed_tasks": sum(w.processed_tasks for w in active_workers),
            "total_failed_tasks": sum(w.failed_tasks for w in active_workers),
        }
    
    def should_scale_up(self) -> bool:
        """判断是否应该扩容"""
        if not self.scaling_enabled:
            return False
        
        # 检查冷却时间
        if datetime.now() - self.last_scale_action < timedelta(seconds=self.config.scale_cooldown_seconds):
            return False
        
        # 检查是否已达到最大Worker数
        active_workers = len([w for w in self.workers.values() if w.status in ["active", "overloaded"]])
        if active_workers >= self.config.max_workers:
            return False
        
        # 检查负载指标
        metrics = self.get_cluster_metrics()
        return (metrics["avg_cpu_percent"] > self.config.scale_up_threshold or
                metrics["avg_memory_percent"] > self.config.scale_up_threshold)
    
    def should_scale_down(self) -> bool:
        """判断是否应该缩容"""
        if not self.scaling_enabled:
            return False
        
        # 检查冷却时间
        if datetime.now() - self.last_scale_action < timedelta(seconds=self.config.scale_cooldown_seconds):
            return False
        
        # 检查是否已达到最小Worker数
        active_workers = len([w for w in self.workers.values() if w.status in ["active", "overloaded"]])
        if active_workers <= self.config.min_workers:
            return False
        
        # 检查负载指标
        metrics = self.get_cluster_metrics()
        idle_workers = [w for w in self.workers.values() if w.status == "idle"]
        
        return (metrics["avg_cpu_percent"] < self.config.scale_down_threshold and
                metrics["avg_memory_percent"] < self.config.scale_down_threshold and
                len(idle_workers) > 0)
    
    def get_scale_recommendation(self) -> Dict[str, any]:
        """获取扩展建议"""
        recommendation = {
            "action": "none",
            "reason": "",
            "target_workers": len(self.workers),
            "current_metrics": self.get_cluster_metrics()
        }
        
        if self.should_scale_up():
            recommendation["action"] = "scale_up"
            recommendation["reason"] = "High CPU/Memory usage detected"
            recommendation["target_workers"] = min(
                len(self.workers) + 1, 
                self.config.max_workers
            )
        elif self.should_scale_down():
            recommendation["action"] = "scale_down"
            recommendation["reason"] = "Low CPU/Memory usage with idle workers"
            recommendation["target_workers"] = max(
                len(self.workers) - 1, 
                self.config.min_workers
            )
        
        return recommendation
    
    def cleanup_failed_workers(self) -> List[str]:
        """清理失败的Worker"""
        failed_workers = []
        current_time = datetime.now()
        
        for worker_id, worker in list(self.workers.items()):
            if (current_time - worker.last_heartbeat > 
                timedelta(seconds=self.config.health_check_interval * 3)):
                failed_workers.append(worker_id)
                del self.workers[worker_id]
                logger.warning(f"Removed failed worker: {worker_id}")
        
        return failed_workers


class WorkerHealthMonitor:
    """Worker健康监控器"""
    
    def __init__(self, worker_id: str, scaler: WorkerScaler):
        self.worker_id = worker_id
        self.scaler = scaler
        self.start_time = datetime.now()
        self.processed_tasks = 0
        self.failed_tasks = 0
        
    def get_system_metrics(self) -> Dict[str, float]:
        """获取系统指标"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory_percent,
                "memory_available": memory.available,
                "memory_used": memory.used,
            }
        except Exception as e:
            logger.error(f"Failed to get system metrics: {e}")
            return {
                "cpu_percent": 0.0,
                "memory_percent": 0.0,
                "memory_available": 0,
                "memory_used": 0,
            }
    
    def report_metrics(self, active_tasks: int = 0) -> None:
        """报告指标到扩展器"""
        metrics = self.get_system_metrics()
        
        self.scaler.update_worker_metrics(
            worker_id=self.worker_id,
            cpu_percent=metrics["cpu_percent"],
            memory_percent=metrics["memory_percent"],
            active_tasks=active_tasks,
            processed_tasks=self.processed_tasks,
            failed_tasks=self.failed_tasks
        )
    
    def task_started(self) -> None:
        """任务开始"""
        pass  # 可以在这里添加任务开始的逻辑
    
    def task_completed(self) -> None:
        """任务完成"""
        self.processed_tasks += 1
    
    def task_failed(self) -> None:
        """任务失败"""
        self.failed_tasks += 1
    
    def get_worker_info(self) -> Dict[str, any]:
        """获取Worker信息"""
        uptime = datetime.now() - self.start_time
        metrics = self.get_system_metrics()
        
        return {
            "worker_id": self.worker_id,
            "uptime_seconds": int(uptime.total_seconds()),
            "processed_tasks": self.processed_tasks,
            "failed_tasks": self.failed_tasks,
            "success_rate": (
                self.processed_tasks / (self.processed_tasks + self.failed_tasks) * 100
                if (self.processed_tasks + self.failed_tasks) > 0 else 100.0
            ),
            "system_metrics": metrics,
        }


# 全局扩展器实例
_scaler = None
_health_monitor = None


def get_scaler() -> WorkerScaler:
    """获取全局扩展器实例"""
    global _scaler
    if _scaler is None:
        config = ScalingConfig(
            min_workers=int(os.getenv("MIN_WORKERS", "1")),
            max_workers=int(os.getenv("MAX_WORKERS", "10")),
            target_cpu_percent=float(os.getenv("TARGET_CPU_PERCENT", "70.0")),
            target_memory_percent=float(os.getenv("TARGET_MEMORY_PERCENT", "80.0")),
            scale_up_threshold=float(os.getenv("SCALE_UP_THRESHOLD", "85.0")),
            scale_down_threshold=float(os.getenv("SCALE_DOWN_THRESHOLD", "30.0")),
        )
        _scaler = WorkerScaler(config)
    return _scaler


def get_health_monitor(worker_id: str = None) -> WorkerHealthMonitor:
    """获取健康监控器实例"""
    global _health_monitor
    if _health_monitor is None:
        if worker_id is None:
            worker_id = f"worker-{os.getpid()}"
        _health_monitor = WorkerHealthMonitor(worker_id, get_scaler())
    return _health_monitor
