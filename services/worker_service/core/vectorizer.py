"""
向量化处理器
专门处理BGE-M3向量化任务，基于原向量化服务的实现
支持环境感知：自动适配Celery异步环境和同步测试环境
"""

import gc
import os
import sys
import time
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

# 在导入LangChain之前禁用遥测，防止PostHog连接
os.environ.setdefault("POSTHOG_HOST", "")
os.environ.setdefault("POSTHOG_PROJECT_ID", "")
os.environ.setdefault("POSTHOG_API_KEY", "")
os.environ.setdefault("POSTHOG_FEATURE_FLAGS", "false")
os.environ.setdefault("LANGCHAIN_TRACING_V2", "false")
os.environ.setdefault("LANGCHAIN_ANALYTICS", "false")
os.environ.setdefault("LANGCHAIN_TRACING", "false")
os.environ.setdefault("LANGCHAIN_TRACKING", "false")
os.environ.setdefault("TELEMETRY_DISABLED", "true")
os.environ.setdefault("DO_NOT_TRACK", "1")

from langchain.schema import Document
import chromadb

from shared.utils.logger import get_logger
from services.worker_service.config import config
from services.worker_service.core.embedding_client import SyncEmbeddingServiceClient
from shared.models.file_info import FileInfo

logger = get_logger(__name__)

# 智能分块配置常量
CHUNK_CONFIG = {
    "target_chars_per_chunk": 3072,  # 3KB per chunk (目标大小)
    "min_chunk_size_chars": 2048,    # 2KB minimum chunk size (最小允许)
    "max_chunk_size_chars": 5120,    # 5KB maximum chunk size (最大允许)
    "absolute_max_chars": 8192,      # 8KB absolute maximum (绝对上限)
    "min_rows_per_chunk": 5,         # 最少行数（避免过小块）
    "max_total_chunks": 200,         # 最大总块数（增加以处理更多数据）
    "max_stats_columns": 3,          # 最多显示统计信息的列数
    "chunk_size_tolerance": 0.3,     # 块大小容忍度（30%）
}


class VectorizationProcessor:
    """向量化处理器 - 支持环境感知"""

    def __init__(self):
        self.embedding_service = None
        self.chroma_client = chromadb.HttpClient(
            host=config.chroma_host, port=config.chroma_port
        )
        self.chunk_size = config.chunk_size
        self.chunk_overlap = config.chunk_overlap
        self.max_chunks = config.max_chunks_per_file

        # 环境检测
        self._is_celery_env = self._detect_celery_environment()
        self._is_sync_test_env = self._detect_sync_test_environment()

        logger.info("向量化处理器初始化完成")
        env_type = 'Celery' if self._is_celery_env else 'Sync Test' if self._is_sync_test_env else 'Unknown'
        logger.info(f"  运行环境: {env_type}")
        logger.info(f"  ChromaDB URL: {config.chroma_url}")
        logger.info(f"  ChromaDB API版本: {config.chroma_api_version}")
        logger.info(f"  集合前缀: {config.chroma_collection_prefix}")

    def _detect_celery_environment(self) -> bool:
        """检测是否在Celery worker环境中运行"""
        try:
            # 检查Celery相关的环境变量
            env_indicators = [
                'CELERY_WORKER_RUNNING' in os.environ,
                'celery' in os.environ.get('_', '').lower(),
                'CELERY_CURRENT_TASK' in os.environ,
            ]

            # 检查是否有Celery相关的进程名
            celery_in_argv = any('celery' in arg.lower() for arg in sys.argv)

            # 检查是否在Celery任务上下文中
            try:
                from celery import current_task
                in_task_context = current_task is not None
            except (ImportError, RuntimeError):
                in_task_context = False

            # 检查进程名
            try:
                import psutil
                current_process = psutil.Process()
                process_name = current_process.name().lower()
                celery_in_process = 'celery' in process_name
            except (ImportError, Exception):
                celery_in_process = False

            is_celery = any(env_indicators) or celery_in_argv or in_task_context or celery_in_process
            logger.debug(
                f"Celery环境检测结果: {is_celery} "
                f"(env: {any(env_indicators)}, argv: {celery_in_argv}, "
                f"task: {in_task_context}, process: {celery_in_process})"
            )
            return is_celery

        except Exception as e:
            logger.debug(f"Celery环境检测失败: {e}")
            return False

    def _detect_sync_test_environment(self) -> bool:
        """检测是否在同步测试环境中运行"""
        try:
            # 检查测试相关的环境变量和模块
            test_indicators = [
                'test' in os.environ.get('PYTEST_CURRENT_TEST', '').lower(),
                'test' in __file__.lower(),
                any('test' in arg.lower() for arg in sys.argv),
            ]

            is_test = any(test_indicators)
            logger.debug(f"同步测试环境检测结果: {is_test}")
            return is_test

        except Exception as e:
            logger.debug(f"同步测试环境检测失败: {e}")
            return False

    def _get_embedding_service(self):
        """延迟初始化嵌入服务客户端 - 环境感知"""
        if self.embedding_service is None:
            self.embedding_service = SyncEmbeddingServiceClient(
                base_url=config.embedding_service_url,
                timeout=config.embedding_service_timeout,
                max_retries=config.embedding_service_max_retries,
            )

            # 根据环境决定是否执行健康检查
            if self._is_sync_test_env:
                logger.info("嵌入服务客户端创建成功（同步测试环境，跳过健康检查）")
            elif self._is_celery_env:
                logger.info("嵌入服务客户端创建成功（Celery环境，支持健康检查）")
            else:
                logger.info("嵌入服务客户端创建成功（默认环境）")

        return self.embedding_service

    def health_check(self) -> Dict[str, Any]:
        """
        环境感知的健康检查
        根据运行环境选择合适的检查策略
        """
        if self._is_sync_test_env:
            return self._sync_test_health_check()
        elif self._is_celery_env:
            return self._celery_health_check()
        else:
            return self._default_health_check()

    def _sync_test_health_check(self) -> Dict[str, Any]:
        """同步测试环境的健康检查（简化版）"""
        logger.info("执行同步测试环境健康检查（跳过异步操作）")

        try:
            # 只检查基本的服务初始化
            embedding_service = self._get_embedding_service()
            embedding_healthy = embedding_service is not None

            # 简单的ChromaDB连接检查（不执行实际操作）
            chroma_healthy = self.chroma_client is not None

            return {
                'healthy': embedding_healthy and chroma_healthy,
                'embedding_service': embedding_healthy,
                'chromadb': chroma_healthy,
                'environment': 'sync_test',
                'note': '跳过异步操作以避免事件循环冲突'
            }

        except Exception as e:
            logger.error(f"同步测试环境健康检查失败: {e}")
            return {
                'healthy': False,
                'error': str(e),
                'environment': 'sync_test'
            }

    def _celery_health_check(self) -> Dict[str, Any]:
        """Celery环境的完整健康检查"""
        logger.info("执行Celery环境完整健康检查")

        try:
            # 检查嵌入服务
            embedding_service = self._get_embedding_service()
            embedding_healthy = False

            if embedding_service:
                try:
                    # 在Celery环境中，避免调用异步方法，只检查服务是否存在
                    # 因为Celery worker中已经有运行的事件循环，调用异步方法会冲突
                    embedding_healthy = embedding_service is not None
                    logger.info("Celery环境：跳过异步健康检查，仅验证服务实例存在")
                except Exception as e:
                    logger.warning(f"嵌入服务健康检查失败: {e}")
                    embedding_healthy = False

            # 检查ChromaDB连接
            chroma_healthy = False
            try:
                version_info = self.chroma_client.get_version()
                chroma_healthy = version_info is not None
                logger.info(f"ChromaDB版本: {version_info}")
            except Exception as e:
                logger.error(f"ChromaDB连接检查失败: {e}")
                chroma_healthy = False

            overall_healthy = embedding_healthy and chroma_healthy

            return {
                'healthy': overall_healthy,
                'embedding_service': embedding_healthy,
                'chromadb': chroma_healthy,
                'environment': 'celery',
                'note': '完整健康检查已执行'
            }

        except Exception as e:
            logger.error(f"Celery环境健康检查失败: {e}")
            return {
                'healthy': False,
                'error': str(e),
                'environment': 'celery'
            }

    def _default_health_check(self) -> Dict[str, Any]:
        """默认健康检查"""
        logger.info("执行默认健康检查")

        try:
            # 基本检查
            embedding_service = self._get_embedding_service()
            embedding_healthy = embedding_service is not None
            chroma_healthy = self.chroma_client is not None

            return {
                'healthy': embedding_healthy and chroma_healthy,
                'embedding_service': embedding_healthy,
                'chromadb': chroma_healthy,
                'environment': 'default',
                'note': '基本健康检查'
            }

        except Exception as e:
            logger.error(f"默认健康检查失败: {e}")
            return {
                'healthy': False,
                'error': str(e),
                'environment': 'default'
            }

    def _get_collection_name(self, file_code: str) -> str:
        """获取集合名称"""
        return f"{config.chroma_collection_prefix}{file_code}"

    def _create_chroma_collection(self, collection_name: str) -> bool:
        """创建ChromaDB集合"""
        try:
            # 尝试获取集合，如果不存在则创建
            try:
                self.chroma_client.get_collection(name=collection_name)
                logger.info(f"集合已存在: {collection_name}")
                return True
            except Exception:
                # 集合不存在，创建新集合
                self.chroma_client.create_collection(
                    name=collection_name,
                    metadata={
                        "created_at": datetime.now().isoformat(),
                        "service": "worker-service",
                        "version": config.service_version,
                    },
                )
                logger.info(f"集合创建成功: {collection_name}")
                return True

        except Exception as e:
            logger.error(f"创建集合失败: {e}")
            return False

    def process_csv_file(self, file_path: str, file_code: str) -> List[Document]:
        """
        处理CSV文件 - 基于原有CSV处理逻辑
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding="utf-8")
            logger.info(f"CSV文件读取成功，共 {len(df)} 行，{len(df.columns)} 列")

            # 使用原有的分块逻辑创建文档
            documents = self._create_chunked_documents_from_df(df, file_path, file_code)

            logger.info(f"✅ CSV文件处理完成，总共创建了 {len(documents)} 个文档块")
            return documents

        except Exception as e:
            logger.error(f"处理CSV文件失败: {e}")
            raise

    def process_excel_file(self, file_path: str, file_code: str) -> List[Document]:
        """
        处理Excel文件 - 基于原有逻辑
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            logger.info(f"Excel文件读取成功，共 {len(df)} 行，{len(df.columns)} 列")

            # 使用相同的分块逻辑
            documents = self._create_chunked_documents_from_df(df, file_path, file_code)

            logger.info(f"✅ Excel文件处理完成，总共创建了 {len(documents)} 个文档块")
            return documents

        except Exception as e:
            logger.error(f"处理Excel文件失败: {e}")
            raise

    def _create_chunked_documents_from_df(
        self, df: pd.DataFrame, file_path: str, file_code: str
    ) -> List[Document]:
        """
        【动态分块版】创建分块文档，使用动态累积策略。

        新的策略特点：
        1. 动态累积：逐行累积内容，达到目标大小时创建块
        2. 智能调整：根据实际内容长度动态决定块边界
        3. 完整性保护：绝不切断单行数据
        4. 合理分布：块大小在合理范围内分布，避免过度集中

        Args:
            df: DataFrame数据
            file_path: 文件路径
            file_code: 文件编码

        Returns:
            分块文档列表
        """
        total_rows = len(df)
        if total_rows == 0:
            logger.warning("数据为空，返回空文档列表")
            return []

        logger.info(f"开始动态分块：总行数={total_rows}")

        # 1. 预估总块数和调整目标大小
        target_chunk_size, estimated_chunks = self._calculate_dynamic_chunk_params(df)
        logger.info(f"目标块大小: {target_chunk_size} 字符, 预估块数: {estimated_chunks}")

        # 2. 创建基础元数据
        base_metadata = {
            "source": file_path,
            "file_name": Path(file_path).name,
            "file_code": file_code,
            "total_rows": total_rows,
            "data_type": "csv_risk_data",
            "created_at": datetime.now().isoformat(),
        }

        # 3. 动态累积分块
        documents = []
        current_chunk_rows = []
        chunk_id = 0

        for row_idx in range(total_rows):
            # 获取当前行
            current_row = df.iloc[row_idx:row_idx+1]

            # 估算添加这行后的内容大小
            if current_chunk_rows:
                current_rows_df = pd.DataFrame([df.iloc[i] for i in current_chunk_rows])
                temp_chunk_df = pd.concat([current_rows_df, current_row], ignore_index=True)
            else:
                temp_chunk_df = current_row
            temp_content = self._convert_chunk_to_markdown(
                temp_chunk_df, chunk_id,
                current_chunk_rows[0] if current_chunk_rows else row_idx,
                row_idx, total_rows, file_code
            )
            temp_size = len(temp_content)

            # 决策：是否应该创建当前块
            should_create_chunk = (
                temp_size > target_chunk_size and  # 超过目标大小
                len(current_chunk_rows) >= CHUNK_CONFIG["min_rows_per_chunk"] and  # 满足最小行数
                len(current_chunk_rows) > 0  # 当前块不为空
            )

            # 绝对大小限制检查
            if temp_size > CHUNK_CONFIG["absolute_max_chars"]:
                should_create_chunk = True
                logger.warning(f"块大小达到绝对上限 {CHUNK_CONFIG['absolute_max_chars']}，强制分块")

            if should_create_chunk:
                # 创建当前块（不包含当前行）
                chunk_df = pd.DataFrame([df.iloc[i] for i in current_chunk_rows])
                chunk_content = self._convert_chunk_to_markdown(
                    chunk_df, chunk_id,
                    current_chunk_rows[0], current_chunk_rows[-1],
                    total_rows, file_code
                )

                doc = Document(
                    page_content=chunk_content,
                    metadata={
                        **base_metadata,
                        "chunk_id": chunk_id,
                        "chunk_start_row": current_chunk_rows[0],
                        "chunk_end_row": current_chunk_rows[-1],
                        "rows_in_chunk": len(current_chunk_rows),
                        "chunk_size_chars": len(chunk_content),
                    }
                )
                documents.append(doc)
                chunk_id += 1

                # 重置当前块，从当前行开始新块
                current_chunk_rows = [row_idx]
            else:
                # 添加当前行到当前块
                current_chunk_rows.append(row_idx)

            # 检查是否达到最大块数限制
            if len(documents) >= CHUNK_CONFIG["max_total_chunks"]:
                logger.warning(f"达到最大块数限制 {CHUNK_CONFIG['max_total_chunks']}，停止处理")
                break

        # 4. 处理最后一个块
        if current_chunk_rows and len(documents) < CHUNK_CONFIG["max_total_chunks"]:
            chunk_df = pd.DataFrame([df.iloc[i] for i in current_chunk_rows])
            chunk_content = self._convert_chunk_to_markdown(
                chunk_df, chunk_id,
                current_chunk_rows[0], current_chunk_rows[-1],
                total_rows, file_code
            )

            doc = Document(
                page_content=chunk_content,
                metadata={
                    **base_metadata,
                    "chunk_id": chunk_id,
                    "chunk_start_row": current_chunk_rows[0],
                    "chunk_end_row": current_chunk_rows[-1],
                    "rows_in_chunk": len(current_chunk_rows),
                    "chunk_size_chars": len(chunk_content),
                }
            )
            documents.append(doc)

        # 5. 更新所有文档的总块数信息
        for doc in documents:
            doc.metadata["total_chunks"] = len(documents)

        # 6. 统计信息
        if documents:
            chunk_sizes = [doc.metadata["chunk_size_chars"] for doc in documents]
            rows_per_chunk = [doc.metadata["rows_in_chunk"] for doc in documents]

            logger.info(f"✅ 动态分块完成：总共创建了 {len(documents)} 个文档块")
            logger.info(f"块大小范围: {min(chunk_sizes)} - {max(chunk_sizes)} 字符")
            logger.info(f"平均块大小: {sum(chunk_sizes) / len(chunk_sizes):.0f} 字符")
            logger.info(f"每块行数范围: {min(rows_per_chunk)} - {max(rows_per_chunk)} 行")
            logger.info(f"平均每块行数: {sum(rows_per_chunk) / len(rows_per_chunk):.1f} 行")

        return documents

    def _calculate_dynamic_chunk_params(self, df: pd.DataFrame) -> tuple[int, int]:
        """
        计算动态分块参数：目标块大小和预估块数

        Args:
            df: DataFrame数据

        Returns:
            (目标块大小, 预估块数) 的元组
        """
        total_rows = len(df)

        # 1. 估算平均每行的字符数
        sample_size = min(10, total_rows)
        if sample_size == 0:
            return CHUNK_CONFIG["target_chars_per_chunk"], 1

        try:
            sample_df = df.head(sample_size)
            sample_markdown = sample_df.to_markdown(index=False)
            avg_chars_per_row = len(sample_markdown) / sample_size
        except Exception as e:
            logger.warning(f"估算字符数失败，使用默认值: {e}")
            avg_chars_per_row = 200  # 保守估计

        # 2. 估算总内容大小
        estimated_total_chars = avg_chars_per_row * total_rows

        # 3. 计算理想块数
        base_target_size = CHUNK_CONFIG["target_chars_per_chunk"]
        ideal_chunks = max(1, int(estimated_total_chars / base_target_size))

        # 4. 限制块数在合理范围内
        max_chunks = CHUNK_CONFIG["max_total_chunks"]
        if ideal_chunks > max_chunks:
            # 如果预估块数过多，增大目标块大小
            adjusted_target_size = int(estimated_total_chars / max_chunks)
            # 但不能超过最大限制
            adjusted_target_size = min(adjusted_target_size, CHUNK_CONFIG["max_chunk_size_chars"])
            final_chunks = max_chunks
        else:
            adjusted_target_size = base_target_size
            final_chunks = ideal_chunks

        # 5. 确保目标大小在合理范围内
        min_size = CHUNK_CONFIG["min_chunk_size_chars"]
        max_size = CHUNK_CONFIG["max_chunk_size_chars"]
        final_target_size = max(min_size, min(adjusted_target_size, max_size))

        logger.info(f"动态参数计算: 平均每行{avg_chars_per_row:.1f}字符, "
                    f"预估总大小{estimated_total_chars:,}字符, "
                    f"理想块数{ideal_chunks}, 最终目标{final_target_size}字符")

        return final_target_size, final_chunks

    def _calculate_optimal_rows_per_chunk(self, df: pd.DataFrame) -> int:
        """
        根据数据特征智能计算最优的每块行数

        Args:
            df: DataFrame数据

        Returns:
            最优的每块行数
        """
        total_rows = len(df)

        # 1. 估算平均每行的字符数
        sample_size = min(10, len(df))
        if sample_size == 0:
            logger.warning("数据为空，使用默认分块大小")
            return CHUNK_CONFIG["min_rows_per_chunk"]

        try:
            sample_df = df.head(sample_size)
            sample_markdown = sample_df.to_markdown(index=False)
            avg_chars_per_row = len(sample_markdown) / sample_size
        except Exception as e:
            logger.warning(f"估算字符数失败，使用默认值: {e}")
            avg_chars_per_row = 100

        # 2. 基于字符数限制计算行数
        target_chars_per_chunk = CHUNK_CONFIG["target_chars_per_chunk"]
        rows_by_chars = max(1, int(target_chars_per_chunk / avg_chars_per_row))

        # 3. 基于总行数计算合理的块数
        base_rows = CHUNK_CONFIG["base_rows_per_chunk"]
        max_chunks = CHUNK_CONFIG["max_total_chunks"]
        target_total_chunks = max(1, total_rows // base_rows)
        if target_total_chunks > max_chunks:
            target_total_chunks = max_chunks
        rows_by_total = max(1, total_rows // target_total_chunks)

        # 4. 取两种方法的平衡值
        optimal_rows = min(rows_by_chars, rows_by_total)

        # 5. 设置合理的边界
        min_rows = CHUNK_CONFIG["min_rows_per_chunk"]
        max_rows = CHUNK_CONFIG["max_rows_per_chunk"]
        optimal_rows = max(min_rows, min(optimal_rows, max_rows))

        logger.info(f"计算最优分块大小: 平均每行{avg_chars_per_row:.1f}字符, "
                    f"按字符计算={rows_by_chars}行, 按总数计算={rows_by_total}行, "
                    f"最终选择={optimal_rows}行")

        return optimal_rows

    def _convert_chunk_to_markdown(
        self, chunk_df: pd.DataFrame, chunk_id: int, start_row: int,
        end_row: int, total_rows: int, file_code: str
    ) -> str:
        """
        将数据块转换为markdown格式，包含适当的上下文信息

        Args:
            chunk_df: 数据块
            chunk_id: 块ID
            start_row: 起始行号
            end_row: 结束行号
            total_rows: 总行数
            file_code: 文件编码

        Returns:
            markdown格式的内容
        """
        # 1. 添加块标识和上下文
        header = [
            f"## 风控数据块 {chunk_id + 1}",
            f"**文件**: {file_code}",
            f"**数据范围**: 第{start_row + 1}行 - 第{end_row + 1}行 (共{total_rows}行)",
            f"**本块行数**: {len(chunk_df)}",
            f"**数据列**: {', '.join(chunk_df.columns.tolist())}",
            "---"
        ]

        # 2. 添加数据表
        table_content = chunk_df.to_markdown(index=False)

        # 3. 添加简单的统计信息（仅数值列）
        stats_parts = []
        numeric_cols = chunk_df.select_dtypes(include=["number"]).columns
        if not numeric_cols.empty and len(chunk_df) > 1:
            stats_parts.append("### 本块数值统计")
            max_stats_cols = CHUNK_CONFIG["max_stats_columns"]
            for col in numeric_cols[:max_stats_cols]:  # 限制显示的数值列数，避免内容过长
                if not chunk_df[col].empty:
                    stats = chunk_df[col].describe()
                    stats_parts.append(
                        f"- **{col}**: 均值={stats.get('mean', 0):.2f}, "
                        f"最小值={stats.get('min', 'N/A')}, "
                        f"最大值={stats.get('max', 'N/A')}"
                    )

        # 4. 拼接所有部分
        all_parts = header + [table_content]
        if stats_parts:
            all_parts.extend([""] + stats_parts)

        return "\n\n".join(all_parts)

    def _split_large_chunk(
        self, chunk_df: pd.DataFrame, start_row: int, file_code: str,
        base_metadata: Dict[str, Any], base_chunk_id: int
    ) -> List[Document]:
        """
        对过大的数据块进行进一步分割

        Args:
            chunk_df: 过大的数据块
            start_row: 起始行号
            file_code: 文件编码
            base_metadata: 基础元数据
            base_chunk_id: 基础块ID

        Returns:
            分割后的文档列表
        """
        documents = []
        sub_chunk_size = max(5, len(chunk_df) // 3)  # 分成更小的块

        for i in range(0, len(chunk_df), sub_chunk_size):
            sub_chunk = chunk_df.iloc[i:i + sub_chunk_size]
            sub_start_row = start_row + i
            sub_end_row = start_row + i + len(sub_chunk) - 1

            content = self._convert_chunk_to_markdown(
                sub_chunk, base_chunk_id, sub_start_row, sub_end_row,
                base_metadata["total_rows"], file_code
            )

            doc = Document(
                page_content=content,
                metadata={
                    **base_metadata,
                    "chunk_id": f"{base_chunk_id}_{i // sub_chunk_size}",
                    "chunk_start_row": sub_start_row,
                    "chunk_end_row": sub_end_row,
                    "rows_in_chunk": len(sub_chunk),
                    "chunk_size_chars": len(content),
                    "is_sub_chunk": True,
                }
            )
            documents.append(doc)

        logger.info(f"大块分割: 将{len(chunk_df)}行分割为{len(documents)}个子块")
        return documents

    def store_documents(
        self, file_code: str, documents: List[Document], file_info: FileInfo
    ) -> bool:
        """
        存储文档到ChromaDB - 环境感知版本
        """
        if self._is_celery_env:
            return self._store_documents_celery(file_code, documents, file_info)
        else:
            return self._store_documents_default(file_code, documents, file_info)

    def _store_documents_celery(
        self, file_code: str, documents: List[Document], file_info: FileInfo
    ) -> bool:
        """
        Celery环境中的文档存储 - 使用HTTP请求避免异步调用冲突
        """
        try:
            collection_name = self._get_collection_name(file_code)

            # 创建集合
            collection_created = self._create_chroma_collection(collection_name)
            if not collection_created:
                return False

            logger.info(f"Celery环境：使用HTTP请求生成嵌入向量: {len(documents)} 个文档")
            start_time = time.time()

            # 使用HTTP请求调用嵌入服务，避免异步调用冲突
            texts = [doc.page_content for doc in documents]
            embeddings = self._get_embeddings_via_http(texts)

            if embeddings is None:
                logger.error("HTTP嵌入请求失败")
                return False

            embedding_time = time.time() - start_time
            logger.info(f"嵌入向量生成完成，耗时 {embedding_time:.2f}s")

            # 准备存储数据
            ids = [f"{file_code}_{i}" for i in range(len(documents))]
            metadatas = [doc.metadata for doc in documents]

            # 存储到ChromaDB
            collection = self.chroma_client.get_collection(name=collection_name)
            collection.add(
                ids=ids, embeddings=embeddings, documents=texts, metadatas=metadatas
            )

            logger.info(f"文档存储成功 (Celery模式): {file_code}, {len(documents)} 个文档")
            return True

        except Exception as e:
            logger.error(f"Celery环境存储文档失败: {e}")
            return False

    def _get_embeddings_via_http(self, texts: List[str]) -> Optional[List[List[float]]]:
        """
        通过HTTP请求获取嵌入向量，避免异步调用冲突
        """
        try:
            import requests

            url = f"{config.embedding_service_url}/api/v1/embed/documents"
            payload = {
                "texts": texts,
                "normalize": True
            }

            response = requests.post(
                url,
                json=payload,
                timeout=config.embedding_service_timeout
            )

            if response.status_code == 200:
                result = response.json()
                return result.get("embeddings", [])
            else:
                logger.error(f"HTTP嵌入请求失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"HTTP嵌入请求异常: {e}")
            return None

    def _store_documents_default(
        self, file_code: str, documents: List[Document], file_info: FileInfo
    ) -> bool:
        """
        默认环境中的文档存储 - 包含完整向量化
        """
        try:
            collection_name = self._get_collection_name(file_code)

            # 创建集合
            collection_created = self._create_chroma_collection(collection_name)
            if not collection_created:
                return False

            # 获取嵌入服务
            embedding_service = self._get_embedding_service()

            # 生成嵌入向量
            logger.info(f"开始生成嵌入向量: {len(documents)} 个文档")
            start_time = time.time()

            texts = [doc.page_content for doc in documents]
            embeddings = embedding_service.embed_documents(texts)

            embedding_time = time.time() - start_time
            logger.info(f"嵌入向量生成完成，耗时 {embedding_time:.2f}s")

            # 准备存储数据
            ids = [f"{file_code}_{i}" for i in range(len(documents))]
            metadatas = [doc.metadata for doc in documents]

            # 存储到ChromaDB
            collection = self.chroma_client.get_collection(name=collection_name)
            collection.add(
                ids=ids, embeddings=embeddings, documents=texts, metadatas=metadatas
            )

            logger.info(f"文档存储成功: {file_code}, {len(documents)} 个文档")
            return True

        except Exception as e:
            logger.error(f"存储文档失败: {e}")
            return False
        finally:
            # 清理嵌入服务客户端
            if self.embedding_service and not self._is_celery_env:
                try:
                    self.embedding_service.close()
                except Exception as e:
                    logger.warning(f"清理嵌入服务客户端时出错: {e}")

    def process_file(self, file_path: str, file_code: str) -> List[Document]:
        """
        处理文件（自动识别类型）
        """
        file_path = Path(file_path)
        file_ext = file_path.suffix.lower()

        if file_ext == ".csv":
            return self.process_csv_file(str(file_path), file_code)
        elif file_ext in [".xlsx", ".xls"]:
            return self.process_excel_file(str(file_path), file_code)
        else:
            raise ValueError(f"Unsupported file type: {file_ext}")

    def cleanup(self):
        """环境感知的资源清理"""
        logger.info(f"开始清理向量化处理器资源 (环境: {self._get_environment_name()})")

        try:
            if self._is_celery_env:
                self._celery_cleanup()
            elif self._is_sync_test_env:
                self._sync_test_cleanup()
            else:
                self._default_cleanup()

            logger.info("向量化处理器资源清理完成")

        except Exception as e:
            logger.error(f"向量化处理器资源清理失败: {e}")

    def _get_environment_name(self) -> str:
        """获取环境名称"""
        if self._is_celery_env:
            return "Celery"
        elif self._is_sync_test_env:
            return "Sync Test"
        else:
            return "Default"

    def _celery_cleanup(self):
        """Celery环境的资源清理"""
        logger.debug("执行Celery环境资源清理")

        # 清理嵌入服务
        if self.embedding_service:
            try:
                # 在Celery环境中，避免调用异步close方法，直接设置为None
                # 因为Celery worker中已经有运行的事件循环，调用异步方法会冲突
                self.embedding_service = None
                logger.debug("嵌入服务客户端已清理 (Celery - 避免异步调用)")
            except Exception as e:
                logger.warning(f"Celery环境清理嵌入服务时出错: {e}")
                self.embedding_service = None

        # 清理ChromaDB客户端
        self._cleanup_chroma_client()

        # 强制垃圾回收
        self._force_garbage_collection()

    def _sync_test_cleanup(self):
        """同步测试环境的资源清理"""
        logger.debug("执行同步测试环境资源清理")

        # 简化清理，避免事件循环问题
        if self.embedding_service:
            try:
                # 直接设置为None，避免调用可能的异步方法
                self.embedding_service = None
                logger.debug("嵌入服务客户端已清理 (Sync Test)")
            except Exception as e:
                logger.warning(f"同步测试环境清理嵌入服务时出错: {e}")

        # 清理ChromaDB客户端
        self._cleanup_chroma_client()

        # 轻量级垃圾回收
        gc.collect()

    def _default_cleanup(self):
        """默认环境的资源清理"""
        logger.debug("执行默认环境资源清理")

        # 清理嵌入服务
        if self.embedding_service:
            try:
                if hasattr(self.embedding_service, 'close'):
                    self.embedding_service.close()
                self.embedding_service = None
                logger.debug("嵌入服务客户端已清理 (Default)")
            except Exception as e:
                logger.warning(f"默认环境清理嵌入服务时出错: {e}")
                self.embedding_service = None

        # 清理ChromaDB客户端
        self._cleanup_chroma_client()

        # 强制垃圾回收
        self._force_garbage_collection()

    def _cleanup_chroma_client(self):
        """清理ChromaDB客户端"""
        try:
            # ChromaDB HttpClient不需要显式关闭，直接设置为None
            self.chroma_client = None
            logger.debug("ChromaDB客户端已清理")
        except Exception as e:
            logger.warning(f"清理ChromaDB客户端时出错: {e}")

    def _force_garbage_collection(self):
        """强制垃圾回收"""
        try:
            # 多次垃圾回收确保资源释放
            for _ in range(3):
                gc.collect()
            logger.debug("强制垃圾回收完成")
        except Exception as e:
            logger.warning(f"强制垃圾回收时出错: {e}")
