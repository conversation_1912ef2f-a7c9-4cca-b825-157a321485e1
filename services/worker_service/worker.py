"""
Worker服务主文件
启动Celery Worker处理向量化和分析任务
"""

import os
import sys
from pathlib import Path

# 在导入任何模块之前设置遥测禁用环境变量
os.environ.setdefault("POSTHOG_HOST", "")
os.environ.setdefault("POSTHOG_PROJECT_ID", "")
os.environ.setdefault("POSTHOG_API_KEY", "")
os.environ.setdefault("POSTHOG_FEATURE_FLAGS", "false")
os.environ.setdefault("POSTHOG_DISABLED", "true")
os.environ.setdefault("DISABLE_POSTHOG", "true")

# LangChain遥测完全禁用
os.environ.setdefault("LANGCHAIN_TRACING_V2", "false")
os.environ.setdefault("LANGCHAIN_ANALYTICS", "false")
os.environ.setdefault("LANGCHAIN_TRACING", "false")
os.environ.setdefault("LANGCHAIN_TRACKING", "false")
os.environ.setdefault("LANGCHAIN_ENDPOINT", "")
os.environ.setdefault("LANGCHAIN_API_KEY", "")
os.environ.setdefault("LANGCHAIN_PROJECT", "")
os.environ.setdefault("LANGCHAIN_SESSION", "")
os.environ.setdefault("LANGCHAIN_CALLBACKS_MANAGER", "")
os.environ.setdefault("LANGCHAIN_HUB_API_KEY", "")
os.environ.setdefault("LANGCHAIN_HUB_API_URL", "")

# 通用遥测禁用
os.environ.setdefault("TELEMETRY_DISABLED", "true")
os.environ.setdefault("DO_NOT_TRACK", "1")
os.environ.setdefault("ANALYTICS_DISABLED", "true")

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 在导入任何其他模块之前，先导入并执行遥测禁用
from shared.utils.telemetry_disable import disable_langchain_telemetry, block_posthog_network

# 再次确保LangChain遥测被禁用
disable_langchain_telemetry()
block_posthog_network()

from shared.utils.logger import setup_logging, get_logger
from shared.celery_config import celery_app, get_worker_startup_args
from shared.redis_config import get_redis_url
from shared.system_compatibility import get_system_config
from services.worker_service.config import config

# 导入所有任务模块，确保任务被注册
from services.worker_service.tasks import (
    vectorize_file,
    process_documents,
    analyze_risk,
    health_check
)

# 设置日志
setup_logging(level=config.log_level, service_name=config.service_name)

logger = get_logger(__name__)


def main():
    """Worker服务主函数"""
    logger.info("=" * 60)
    logger.info(f"🚀 启动 {config.service_name} v{config.service_version}")
    logger.info("=" * 60)

    # 系统兼容性信息
    system_config = get_system_config()
    logger.info(f"🖥️  系统: {system_config.system_name}")
    logger.info(f"🏊 Worker池类型: {system_config.pool_type}")
    logger.info(f"🧵 并发数: {system_config.concurrency}")
    logger.info(f"🔄 内存策略: {system_config.memory_strategy}")

    # 服务配置信息
    logger.info(f"📝 日志级别: {config.log_level}")

    # 嵌入服务配置
    logger.info(f"🔗 嵌入服务URL: {config.embedding_service_url}")
    logger.info(f"⏱️ 嵌入服务超时: {config.embedding_service_timeout}s")
    logger.info(f"🔄 嵌入服务重试: {config.embedding_service_max_retries}次")

    # 依赖服务配置
    logger.info(f"🗄️  ChromaDB: {config.chroma_url}")
    logger.info(f"🔴 Redis: {get_redis_url()}")
    logger.info(f"🤖 LLM提供商: {config.llm_provider}")

    # 任务配置
    logger.info(f"⏱️  任务超时: {config.task_timeout}秒")
    logger.info(f"🔄 最大重试次数: {config.max_retries}")
    logger.info(f"📦 分块大小: {config.chunk_size}")
    logger.info(f"🔗 分块重叠: {config.chunk_overlap}")

    # 内存管理配置
    logger.info(f"🧠 内存监控: {config.enable_memory_monitoring}")
    logger.info(f"🧹 自动清理: {config.auto_cleanup}")
    logger.info(f"⚠️  内存清理阈值: {config.memory_cleanup_threshold}%")
    
    logger.info("✅ Worker服务配置完成")
    logger.info("=" * 60)
    
    # 显示注册的任务
    logger.info("📋 已注册的任务:")
    for task_name in celery_app.tasks:
        if not task_name.startswith('celery.'):
            logger.info(f"  - {task_name}")
    
    logger.info("=" * 60)
    logger.info("🎯 Worker服务已准备就绪，等待任务...")
    # 显示推荐的启动命令
    worker_args = get_worker_startup_args()
    cmd_parts = ["celery", "-A", "services.worker_service.worker", "worker"]
    for key, value in worker_args.items():
        if key == "pool":
            cmd_parts.extend([f"--{key}", str(value)])
        elif key == "concurrency":
            cmd_parts.extend([f"--{key}", str(value)])
        elif key == "max_tasks_per_child" and value is not None:
            cmd_parts.extend([f"--max-tasks-per-child", str(value)])
        elif key == "loglevel":
            cmd_parts.extend([f"--{key}", str(value)])
        elif key == "prefetch_multiplier":
            cmd_parts.extend([f"--prefetch-multiplier", str(value)])

    logger.info("💡 推荐的启动命令:")
    logger.info(f"   {' '.join(cmd_parts)}")
    logger.info("=" * 60)


if __name__ == "__main__":
    main()

    # 如果直接运行此文件，启动Worker
    # 注意：生产环境建议使用celery命令启动
    import subprocess

    # 使用动态配置构建启动命令
    worker_args = get_worker_startup_args()
    cmd = ["celery", "-A", "services.worker_service.worker", "worker"]

    # 添加动态参数
    for key, value in worker_args.items():
        if key == "pool":
            cmd.extend([f"--{key}", str(value)])
        elif key == "concurrency":
            cmd.extend([f"--{key}", str(value)])
        elif key == "max_tasks_per_child" and value is not None:
            cmd.extend([f"--max-tasks-per-child", str(value)])
        elif key == "loglevel":
            cmd.extend([f"--{key}", str(value)])
        elif key == "prefetch_multiplier":
            cmd.extend([f"--prefetch-multiplier", str(value)])

    logger.info(f"🚀 启动命令: {' '.join(cmd)}")

    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        logger.info("Worker服务已停止")
    except Exception as e:
        logger.error(f"Worker服务启动失败: {e}")
