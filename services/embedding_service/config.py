"""
BGE-M3嵌入服务独立配置
支持完全独立部署，自动检测共享组件
"""

import os
import sys
from pathlib import Path
from typing import Optional

try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings

# 智能路径检测 - 支持独立部署和项目内部署
def setup_shared_path():
    """设置共享组件路径"""
    current_dir = Path(__file__).parent

    # 尝试多种路径配置
    possible_roots = [
        current_dir.parent.parent,  # 项目内部署: services/embedding_service -> project_root
        current_dir.parent,         # 独立部署: embedding_service -> shared在同级
        Path("/app"),               # Docker部署: /app
    ]

    for root in possible_roots:
        shared_path = root / "shared"
        if shared_path.exists():
            sys.path.insert(0, str(root))
            return root

    # 如果找不到shared，使用当前目录的父级
    fallback_root = current_dir.parent.parent
    sys.path.insert(0, str(fallback_root))
    return fallback_root

project_root = setup_shared_path()

# 尝试导入共享组件，如果失败则使用本地实现
try:
    from shared.constants import ServiceNames, ServicePorts
    SHARED_AVAILABLE = True
except ImportError:
    # 本地实现基本功能
    class ServiceNames:
        EMBEDDING_SERVICE = "embedding-service"

    class ServicePorts:
        EMBEDDING_SERVICE = 8004

    SHARED_AVAILABLE = False

# GPU相关功能现在由本地gpu_compatibility模块处理
def get_optimal_device():
    """获取最优设备 - 延迟导入避免循环依赖"""
    try:
        from services.embedding_service.core.gpu_compatibility import get_optimal_device as gpu_get_optimal_device
        return gpu_get_optimal_device()
    except ImportError:
        # 回退到基本实现
        import torch
        if torch.cuda.is_available():
            return "cuda"
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return "mps"
        return "cpu"

def should_force_cpu():
    """是否强制使用CPU - 延迟导入避免循环依赖"""
    try:
        from services.embedding_service.core.gpu_compatibility import should_force_cpu as gpu_should_force_cpu
        return gpu_should_force_cpu()
    except ImportError:
        # 回退到基本实现
        return os.getenv("FORCE_CPU", "false").lower() == "true"


class EmbeddingServiceConfig(BaseSettings):
    """嵌入服务配置"""

    # 服务基本信息
    service_name: str = os.getenv("SERVICE_NAME", ServiceNames.EMBEDDING_SERVICE)
    service_version: str = os.getenv("SERVICE_VERSION", "1.0.0")
    host: str = os.getenv("HOST", "0.0.0.0")
    port: int = int(os.getenv("PORT", str(ServicePorts.EMBEDDING_SERVICE)))
    debug: bool = os.getenv("DEBUG", "false").lower() == "true"
    log_level: str = os.getenv("LOG_LEVEL", "INFO")

    # 部署环境
    environment: str = os.getenv("ENVIRONMENT", "development")
    deployment_mode: str = os.getenv("DEPLOYMENT_MODE", "standalone")  # standalone, docker, k8s

    # BGE-M3模型配置
    model_name: str = os.getenv(
        "MODEL_NAME", str(project_root / "models" / "bge-m3-safetensors-only")
    )
    model_cache_dir: Optional[str] = os.getenv("MODEL_CACHE_DIR", "./models")

    # 设备配置
    _optimal_device: str = get_optimal_device()
    device: str = os.getenv("DEVICE", _optimal_device)
    force_cpu: bool = should_force_cpu()

    # 嵌入配置
    batch_size: int = int(os.getenv("BATCH_SIZE", "32"))
    max_length: int = int(os.getenv("MAX_LENGTH", "8192"))
    normalize_embeddings: bool = True

    # 性能配置
    max_concurrent_requests: int = int(os.getenv("MAX_CONCURRENT_REQUESTS", "10"))
    request_timeout: int = int(os.getenv("REQUEST_TIMEOUT", "300"))  # 5分钟

    # 内存管理配置
    enable_memory_monitoring: bool = (
        os.getenv("ENABLE_MEMORY_MONITORING", "true").lower() == "true"
    )
    auto_cleanup: bool = os.getenv("AUTO_CLEANUP", "true").lower() == "true"
    memory_cleanup_threshold: float = float(
        os.getenv("MEMORY_CLEANUP_THRESHOLD", "0.8")
    )  # 80%

    # 缓存配置
    enable_cache: bool = os.getenv("ENABLE_CACHE", "true").lower() == "true"
    cache_ttl: int = int(os.getenv("CACHE_TTL", "3600"))  # 1小时
    max_cache_size: int = int(os.getenv("MAX_CACHE_SIZE", "1000"))

    # API配置
    api_prefix: str = "/api/v1"
    enable_docs: bool = os.getenv("ENABLE_DOCS", "true").lower() == "true"

    # 健康检查配置
    health_check_interval: int = int(os.getenv("HEALTH_CHECK_INTERVAL", "30"))  # 30秒

    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # 忽略额外的环境变量


# 全局配置实例
config = EmbeddingServiceConfig()
