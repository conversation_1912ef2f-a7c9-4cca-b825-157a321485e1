"""
GPU兼容性检测模块 - 专用于嵌入服务
处理GPU设备检测、内存管理、MPS支持等功能
"""

import os
import platform
import torch
from typing import Dict, Any, Optional
from dataclasses import dataclass

from shared.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class GPUCompatibilityConfig:
    """GPU兼容性配置"""
    
    # 设备配置
    device: str
    force_cpu: bool
    
    # GPU特性支持
    cuda_available: bool
    mps_available: bool
    
    # 环境变量
    env_vars: Dict[str, str]
    
    # 设备信息
    device_info: Dict[str, Any]


class GPUCompatibilityDetector:
    """GPU兼容性检测器 - 专用于嵌入服务"""
    
    def __init__(self):
        self.system = platform.system()
        self.system_version = platform.version()
        self.python_version = platform.python_version()
        
        logger.info(f"GPU兼容性检测: {self.system} {self.system_version}")
        logger.info(f"Python版本: {self.python_version}")
        logger.info(f"PyTorch版本: {torch.__version__}")
    
    def detect_optimal_config(self) -> GPUCompatibilityConfig:
        """检测最优GPU配置"""
        
        # 获取设备信息
        device_info = self.get_device_info()
        
        # 检测CUDA和MPS可用性
        cuda_available = device_info.get("cuda_available", False)
        mps_available = device_info.get("mps_available", False)
        
        # 确定最优设备
        device = self._determine_optimal_device(cuda_available, mps_available)
        
        # 检查是否强制使用CPU
        force_cpu = self._should_force_cpu()
        if force_cpu:
            device = "cpu"
            logger.info("强制使用CPU模式")
        
        # 设置环境变量
        env_vars = self._get_environment_variables(device, mps_available)
        
        config = GPUCompatibilityConfig(
            device=device,
            force_cpu=force_cpu,
            cuda_available=cuda_available,
            mps_available=mps_available,
            env_vars=env_vars,
            device_info=device_info
        )
        
        return config
    
    def _determine_optimal_device(self, cuda_available: bool, mps_available: bool) -> str:
        """确定最优设备"""
        
        # 检查用户指定的设备
        user_device = os.getenv("DEVICE", "auto").lower()
        if user_device != "auto":
            logger.info(f"用户指定设备: {user_device}")
            return user_device
        
        # 自动检测最优设备
        if cuda_available:
            logger.info("检测到CUDA，使用GPU加速")
            return "cuda"
        elif mps_available and self.system == "Darwin":
            logger.info("检测到MPS，使用Apple Silicon GPU加速")
            return "mps"
        else:
            logger.info("未检测到GPU，使用CPU模式")
            return "cpu"
    
    def _should_force_cpu(self) -> bool:
        """检查是否应该强制使用CPU"""
        return os.getenv("FORCE_CPU", "false").lower() == "true"
    
    def _get_environment_variables(self, device: str, mps_available: bool) -> Dict[str, str]:
        """获取GPU相关环境变量"""
        env_vars = {}
        
        # MPS相关环境变量
        if device == "mps" or (mps_available and self.system == "Darwin"):
            env_vars.update({
                "PYTORCH_ENABLE_MPS_FALLBACK": "1",  # MPS回退
                "PYTORCH_MPS_HIGH_WATERMARK_RATIO": "0.0",  # 禁用MPS高水位
            })
            logger.info("设置MPS相关环境变量")
        
        return env_vars
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取详细的设备信息"""
        info = {
            "system": self.system,
            "python_version": self.python_version,
            "torch_version": torch.__version__,
        }
        
        try:
            # CUDA信息
            if torch.cuda.is_available():
                info.update({
                    "cuda_available": True,
                    "cuda_version": torch.version.cuda,
                    "gpu_count": torch.cuda.device_count(),
                    "gpu_names": [torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())],
                    "gpu_memory": [
                        {
                            "device": i,
                            "total_memory_gb": torch.cuda.get_device_properties(i).total_memory / 1024**3,
                            "name": torch.cuda.get_device_name(i)
                        }
                        for i in range(torch.cuda.device_count())
                    ]
                })
            else:
                info["cuda_available"] = False
            
            # MPS信息 (macOS)
            if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                info["mps_available"] = True
            else:
                info["mps_available"] = False
                
        except Exception as e:
            logger.debug(f"获取设备信息时出错: {e}")
            info["device_detection_error"] = str(e)
        
        return info
    
    def apply_environment_variables(self, config: GPUCompatibilityConfig):
        """应用GPU相关环境变量"""
        for key, value in config.env_vars.items():
            os.environ[key] = value
            logger.info(f"设置GPU环境变量: {key}={value}")
    
    def log_gpu_info(self, config: GPUCompatibilityConfig):
        """记录GPU兼容性信息"""
        logger.info("=" * 60)
        logger.info("🎮 GPU兼容性配置")
        logger.info("=" * 60)
        logger.info(f"设备: {config.device}")
        logger.info(f"强制CPU: {config.force_cpu}")
        logger.info(f"CUDA可用: {config.cuda_available}")
        logger.info(f"MPS可用: {config.mps_available}")
        
        if config.env_vars:
            logger.info("GPU环境变量:")
            for key, value in config.env_vars.items():
                logger.info(f"  {key}={value}")
        
        # 详细设备信息
        device_info = config.device_info
        logger.info("设备详情:")
        for key, value in device_info.items():
            if key == "gpu_memory" and isinstance(value, list):
                logger.info(f"  GPU内存信息:")
                for gpu in value:
                    logger.info(f"    GPU {gpu['device']}: {gpu['name']} - {gpu['total_memory_gb']:.1f}GB")
            else:
                logger.info(f"  {key}: {value}")
        
        logger.info("=" * 60)


# 全局实例
gpu_detector = GPUCompatibilityDetector()
gpu_config = gpu_detector.detect_optimal_config()

# 应用环境变量
gpu_detector.apply_environment_variables(gpu_config)

# 记录配置信息
gpu_detector.log_gpu_info(gpu_config)


def get_gpu_config() -> GPUCompatibilityConfig:
    """获取GPU配置"""
    return gpu_config


def get_optimal_device() -> str:
    """获取最优设备"""
    return gpu_config.device


def should_force_cpu() -> bool:
    """是否应该强制使用CPU"""
    return gpu_config.force_cpu


def is_cuda_available() -> bool:
    """检查CUDA是否可用"""
    return gpu_config.cuda_available


def is_mps_available() -> bool:
    """检查MPS是否可用"""
    return gpu_config.mps_available


def get_device_info() -> Dict[str, Any]:
    """获取设备信息"""
    return gpu_config.device_info


def recommend_device_settings() -> Dict[str, Any]:
    """推荐设备设置"""
    settings = {
        "use_fp16": gpu_config.cuda_available,
        "batch_size": 32,
        "warnings": []
    }
    
    if gpu_config.cuda_available:
        # GPU可用时的推荐设置
        gpu_memory_info = gpu_config.device_info.get("gpu_memory", [])
        if gpu_memory_info:
            min_memory = min(gpu["total_memory_gb"] for gpu in gpu_memory_info)
            if min_memory < 8:
                settings["batch_size"] = 16
                settings["warnings"].append("GPU内存较小，建议使用较小的batch_size")
    else:
        settings["use_fp16"] = False
        settings["batch_size"] = 16
        settings["warnings"].append("未检测到GPU，使用CPU模式")
    
    return settings
