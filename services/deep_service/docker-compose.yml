version: '3.8'

services:
  # Deep服务 - API网关
  deep-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: deep-risk-deep-service
    ports:
      - "${PORT:-8000}:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-false}
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEPLOYMENT_MODE=docker
      
      # 外部服务连接
      - REDIS_URL=redis://redis:6379/0
      - CHROMA_URL=http://chromadb:8000
      - EMBEDDING_SERVICE_URL=http://embedding-service:8004
      
      # LLM配置
      - LLM_PROVIDER=${LLM_PROVIDER:-deepseek}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ENABLE_STREAMING=${ENABLE_STREAMING:-true}
      
      # 文件配置
      - UPLOAD_DIR=/app/data/uploads
      - MAX_FILE_SIZE=${MAX_FILE_SIZE:-100}
      
      # 任务配置
      - TASK_TIMEOUT=${TASK_TIMEOUT:-3600}
      - TASK_RETRY_MAX=${TASK_RETRY_MAX:-3}
    volumes:
      # 文件上传目录
      - ${UPLOAD_PATH:-./data/uploads}:/app/data/uploads
      # 日志目录
      - deep_logs:/app/logs
      # 缓存目录
      - deep_cache:/app/cache
    depends_on:
      redis:
        condition: service_healthy
      chromadb:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - deep-network

  # Redis - 消息队列和缓存
  redis:
    image: redis:7-alpine
    container_name: deep-service-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - deep-network

  # ChromaDB - 向量数据库
  chromadb:
    image: chromadb/chroma:latest
    container_name: deep-service-chromadb
    ports:
      - "${CHROMA_PORT:-8001}:8000"
    volumes:
      - chromadb_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - deep-network

  # 可选：嵌入服务 (如果需要本地运行)
  embedding-service:
    image: deep-risk-embedding:latest  # 需要先构建嵌入服务镜像
    container_name: deep-service-embedding
    ports:
      - "${EMBEDDING_PORT:-8004}:8004"
    environment:
      - HOST=0.0.0.0
      - PORT=8004
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEVICE=${DEVICE:-auto}
    volumes:
      - ${MODEL_PATH:-../../models}:/app/models:ro
      - embedding_cache:/app/cache
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/api/v1/health/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - deep-network
    profiles:
      - with-embedding

  # 可选：Worker服务 (如果需要本地运行)
  worker-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: deep-service-worker
    environment:
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - REDIS_URL=redis://redis:6379/0
      - CHROMA_URL=http://chromadb:8000
      - EMBEDDING_SERVICE_URL=http://embedding-service:8004
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
    volumes:
      - ${UPLOAD_PATH:-./data/uploads}:/app/data/uploads
      - worker_logs:/app/logs
    command: celery -A services.worker_service.worker worker --loglevel=info
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - deep-network
    profiles:
      - with-worker

  # 可选：Flower监控
  flower:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: deep-service-flower
    ports:
      - "${FLOWER_PORT:-5555}:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
    command: celery -A services.worker_service.worker flower --port=5555
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - deep-network
    profiles:
      - monitoring

volumes:
  redis_data:
    driver: local
  chromadb_data:
    driver: local
  deep_logs:
    driver: local
  deep_cache:
    driver: local
  embedding_cache:
    driver: local
  worker_logs:
    driver: local

networks:
  deep-network:
    driver: bridge

# 使用说明:
# 1. 基本启动 (仅Deep服务): docker-compose up -d
# 2. 包含嵌入服务: docker-compose --profile with-embedding up -d
# 3. 包含Worker服务: docker-compose --profile with-worker up -d
# 4. 完整服务: docker-compose --profile with-embedding --profile with-worker --profile monitoring up -d
# 5. 自定义端口: PORT=8001 docker-compose up -d
