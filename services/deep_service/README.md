# Deep服务 - API网关和统一服务

Deep服务是Deep Risk RAG系统的核心API网关，提供文件上传、向量化任务管理、风险分析等统一接口，支持完全独立部署。

## ✨ 特性

- 🚀 **完全独立部署** - 可脱离主项目独立运行
- 🌐 **API网关功能** - 统一的服务入口和路由管理
- 📁 **文件管理** - 支持CSV/Excel文件上传和管理
- ⚡ **异步任务** - 集成Celery进行后台任务处理
- 🔄 **任务管理** - 完整的任务状态跟踪和管理
- 🛡️ **错误处理** - 统一的错误处理和响应格式
- 📊 **监控支持** - 健康检查、指标收集和日志记录
- 🔒 **生产就绪** - 包含安全、限流和CORS配置
- 📦 **Docker支持** - 提供完整的容器化部署方案

## 🏗️ 架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Deep Service (API Gateway)               │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Upload    │  │   Analyze   │  │    Tasks    │         │
│  │     API     │  │     API     │  │     API     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │    Files    │  │   Status    │  │   Results   │         │
│  │     API     │  │     API     │  │     API     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    External Services                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │    Redis    │  │  ChromaDB   │  │  Embedding  │         │
│  │   (Queue)   │  │  (Vector)   │  │   Service   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│  ┌─────────────┐  ┌─────────────┐                          │
│  │   Worker    │  │     LLM     │                          │
│  │  Service    │  │   Provider  │                          │
│  └─────────────┘  └─────────────┘                          │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 方式1：直接启动

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境
cp .env.example .env
# 编辑 .env 文件，设置必要的环境变量

# 3. 启动服务
./start.sh

# 或者使用Python直接启动
python main.py
```

### 方式2：使用启动脚本

```bash
# 开发模式 (支持热重载)
./start.sh --dev

# 生产模式 (多进程)
./start.sh --prod

# 检查环境和依赖
./start.sh --check

# 安装依赖
./start.sh --install

# 运行测试
./start.sh --test
```

### 方式3：Docker部署

```bash
# 基本启动 (仅Deep服务)
docker-compose up -d

# 包含嵌入服务
docker-compose --profile with-embedding up -d

# 包含Worker服务
docker-compose --profile with-worker up -d

# 完整服务栈
docker-compose --profile with-embedding --profile with-worker --profile monitoring up -d
```

服务将在 `http://localhost:8000` 启动。

## 📋 API文档

启动服务后，可以通过以下地址访问API文档：

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

### 主要API端点

#### 文件管理
- `POST /upload/` - 上传单个文件
- `POST /upload/batch` - 批量上传文件
- `GET /files/` - 获取文件列表
- `GET /files/{file_code}` - 获取文件详情
- `DELETE /files/{file_code}` - 删除文件

#### 风险分析
- `POST /analyze/{file_code}` - 开始风险分析
- `POST /analyze/{file_code}/quick` - 快速分析
- `POST /analyze/batch` - 批量分析
- `GET /result/{analysis_id}` - 获取分析结果
- `GET /result/{analysis_id}/status` - 获取分析状态

#### 任务管理
- `GET /tasks/{task_id}` - 获取任务状态
- `POST /tasks/{task_id}/cancel` - 取消任务
- `POST /tasks/{task_id}/retry` - 重试任务
- `GET /tasks/` - 获取队列统计

#### 系统监控
- `GET /health` - 健康检查
- `GET /health/detailed` - 详细健康状态
- `GET /status/{file_code}` - 文件状态查询

## ⚙️ 配置

### 环境变量

主要环境变量配置：

```bash
# 服务配置
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO
DEBUG=false

# 外部服务
REDIS_URL=redis://localhost:6379/0
CHROMA_URL=http://localhost:8001
EMBEDDING_SERVICE_URL=http://localhost:8004

# LLM配置
LLM_PROVIDER=deepseek
DEEPSEEK_API_KEY=your_api_key_here
ENABLE_STREAMING=true

# 文件配置
UPLOAD_DIR=./data/uploads
MAX_FILE_SIZE=100
```

完整配置请参考 `.env.example` 文件。

### 依赖服务

Deep服务需要以下外部服务：

1. **Redis** - 消息队列和缓存
2. **ChromaDB** - 向量数据库
3. **Embedding Service** - 文本嵌入计算
4. **Worker Service** - 后台任务处理 (可选)

## 🔧 开发

### 项目结构

```
services/deep_service/
├── api/                    # API路由
│   ├── analyze.py         # 分析相关API
│   ├── files.py           # 文件管理API
│   ├── health.py          # 健康检查API
│   ├── results.py         # 结果查询API
│   ├── status.py          # 状态查询API
│   ├── tasks.py           # 任务管理API
│   └── upload.py          # 文件上传API
├── core/                  # 核心组件
│   ├── chroma_client.py   # ChromaDB客户端
│   ├── error_handler.py   # 错误处理器
│   ├── middleware.py      # 中间件
│   └── task_manager.py    # 任务管理器
├── models/                # 数据模型
│   ├── requests.py        # 请求模型
│   └── responses.py       # 响应模型
├── config.py              # 配置管理
├── main.py                # 主应用
├── requirements.txt       # 依赖列表
├── start.sh               # 启动脚本
├── Dockerfile             # Docker配置
├── docker-compose.yml     # Docker Compose配置
└── README.md              # 文档
```

### 添加新的API端点

1. 在 `api/` 目录下创建或修改路由文件
2. 在 `models/` 中定义请求和响应模型
3. 在 `main.py` 中注册路由
4. 更新API文档

### 错误处理

使用统一的错误处理机制：

```python
from services.deep_service.core.error_handler import DeepServiceError

# 抛出自定义异常
raise DeepServiceError(
    message="Something went wrong",
    error_code="CUSTOM_ERROR",
    status_code=400,
    details={"key": "value"}
)
```

## 🧪 测试

```bash
# 运行所有测试
./start.sh --test

# 或者直接使用pytest
pytest tests/ -v

# 运行特定测试
pytest tests/test_api.py -v

# 生成覆盖率报告
pytest --cov=services.deep_service tests/
```

## 📊 监控

### 健康检查

- **基本检查**: `GET /health`
- **详细检查**: `GET /health/detailed`
- **存活检查**: `GET /health/live`
- **就绪检查**: `GET /health/ready`

### 日志

日志配置支持多种格式和输出方式：

```bash
# 查看实时日志
tail -f logs/deep_service.log

# Docker环境查看日志
docker-compose logs -f deep-service
```

### 指标收集

支持Prometheus指标收集（可选）：

```bash
# 启用指标收集
ENABLE_METRICS=true

# 访问指标端点
curl http://localhost:9090/metrics
```

## 🚀 部署

### 生产部署建议

1. **环境配置**
   - 设置 `ENVIRONMENT=production`
   - 禁用调试模式 `DEBUG=false`
   - 配置适当的日志级别

2. **安全配置**
   - 限制CORS来源
   - 配置API密钥认证
   - 启用HTTPS

3. **性能优化**
   - 调整Worker进程数
   - 配置连接池大小
   - 启用缓存

4. **监控配置**
   - 配置健康检查
   - 启用指标收集
   - 设置日志聚合

## 🤝 贡献

1. Fork项目
2. 创建功能分支
3. 提交变更
4. 创建Pull Request

## 📄 许可证

MIT License
