# CLAUDE-ZH.md

本文件为 Claude Code (claude.ai/code) 在此代码仓库中工作时提供中文指导。

## 开发命令

### 快速启动
```bash
# 一键启动所有服务
./scripts/quick_start.sh

# 跳过依赖安装
./scripts/quick_start.sh --skip
```

### 独立服务启动
```bash
# Deep服务 (API网关 - 端口8000)
cd services/deep_service/
./start.sh --dev      # 开发模式（自动重载）
./start.sh --prod     # 生产模式（4个工作进程）
./start.sh --test     # 运行服务测试

# 嵌入服务 (BGE-M3 - 端口8004)
cd services/embedding_service/
./start.sh --dev      # 开发模式
./start.sh --check    # 检查Python、依赖、模型、GPU

# Worker服务 (Celery后台任务)
cd services/worker_service/
./start.sh --worker   # 启动Celery工作进程（前台）
./start.sh --flower   # 启动Flower监控（端口5555）
./start.sh --all      # 启动所有Worker服务（后台）

# Worker服务后台启动（生成日志和PID文件）
WORKER_BACKGROUND=true ./start.sh --worker   # 后台启动Worker
FLOWER_BACKGROUND=true ./start.sh --flower   # 后台启动Flower
BEAT_BACKGROUND=true ./start.sh --beat       # 后台启动Beat

# Worker服务管理
./stop.sh --all       # 停止所有服务
./stop.sh worker      # 停止指定服务
./stop.sh --status    # 查看服务状态
./stop.sh --logs worker  # 查看服务日志
./stop.sh --cleanup   # 清理日志和PID文件
```

### Docker部署
```bash
# 完整部署
./scripts/deploy.sh --up
./scripts/deploy.sh --down
./scripts/deploy.sh --restart

# 手动Docker命令
cd deployment/
docker-compose up -d
docker-compose ps
docker-compose logs -f
```

### 测试
```bash
# 运行所有测试
pytest tests/ -v

# 测试特定区域
pytest tests/test_api.py -v
pytest tests/test_upload_api.py -v
pytest --cov=services tests/

# 服务特定测试
cd services/embedding_service && python test_embedding_service.py
cd services/worker_service && python test_celery_vectorizer.py
```

### 代码质量
```bash
# 格式化和代码检查（已配置工具）
black src/ tests/ services/
flake8 src/ tests/ services/
```

### 系统监控
```bash
# 系统监控
./scripts/monitor.sh --status    # 服务状态
./scripts/monitor.sh --logs      # 实时日志
./scripts/monitor.sh --watch     # 持续监控
./scripts/monitor.sh --tasks     # Celery任务状态
```

## 架构概览

### 微服务架构
系统由4个主要服务组成，通过Docker Compose编排：

1. **Deep服务**（端口8000）- 主API网关，处理文件上传、任务协调和用户请求
2. **嵌入服务**（端口8004）- BGE-M3模型服务，用于向量嵌入（GPU优化）
3. **Worker服务** - Celery工作进程，处理后台风险分析和文档处理
4. **基础设施** - Redis（消息队列）、ChromaDB（向量数据库）

### 服务依赖关系
启动顺序至关重要：
1. Redis + ChromaDB（基础设施）
2. 嵌入服务（需要Redis、ChromaDB）
3. Deep服务 + Worker服务（需要上述所有服务）

### 配置系统
- **共享配置**：`shared/config.py` 通过 `SharedConfig` 类提供统一配置管理
- **服务特定配置**：每个服务都有自己的 `config.py` 包含服务特定设置
- **环境配置**：使用 `.env` 文件和环境变量存储敏感数据
- **Docker配置**：`deployment/configs/common.env` 用于容器环境

### 关键架构模式

#### LLM工厂模式
位于 `shared/llm/factory.py`：
- 统一的LLM提供商管理（DeepSeek、OpenAI）
- 工厂模式，通过装饰器注册提供商
- 配置构建器，灵活映射设置
- 实例缓存和LangChain兼容性

#### Redis命名空间隔离
系统使用Redis命名空间隔离不同类型的数据：
- 任务队列、结果和缓存都使用单独的命名空间
- 通过 `shared/redis_config.py` 配置

#### 基于文件的向量隔离
- 每个上传的文件获得唯一的 `file_code` 标识符
- ChromaDB集合按文件分组（`file_{code}`），确保数据隔离
- 向量嵌入通过Celery异步处理

### 任务处理流程
1. 文件上传 → Deep服务 → 生成 `file_code`
2. 异步向量化 → Worker服务 → 调用嵌入服务 → 存储到ChromaDB
3. 风险分析 → Worker服务 → 检索向量 + 调用LLM → 返回结果

## 开发指南

### 添加新的LLM提供商
1. 扩展 `shared/llm/base.py` 添加新的提供商类
2. 使用 `@register_llm_provider` 装饰器注册
3. 在 `ConfigBuilder.build_config_from_settings()` 中添加提供商配置

### 配置管理
- 使用 `shared/config.py` 的 `SharedConfig.from_env()` 获取标准服务配置
- 服务特定配置应继承共享配置的通用模式
- 始终使用 `.validate()` 方法验证配置

### 文件处理模式
- 新文件类型：扩展 `shared/constants.py` 的 `FileTypes.ALLOWED_EXTENSIONS`
- 处理逻辑：添加到 `services/worker_service/tasks/`
- 使用唯一文件编码进行隔离：`shared/utils/file_utils.py`

### 测试策略
- 在 `tests/` 中对各个组件进行单元测试
- 对服务交互进行集成测试
- 使用 `pytest` 和覆盖率报告
- 服务特定测试文件与服务代码并列

### 遥测和隐私保护
系统实施了全面的遥测禁用机制以保护用户隐私：
- 禁用所有PostHog、LangChain和分析遥测数据发送
- 多层次遥测阻断：
  - 启动脚本中的环境变量
  - 代码层面在导入前阻断
  - 共享工具模块 `shared/utils/telemetry_disable.py`
- 测试遥测禁用效果：`python scripts/test_telemetry_fix.py`
- 如果遇到PostHog连接错误，重启相关服务即可解决

## 健康检查和监控

### API健康检查端点
```bash
curl http://localhost:8000/health              # Deep服务
curl http://localhost:8004/api/v1/health/ready # 嵌入服务
curl http://localhost:8001/api/v1/heartbeat    # ChromaDB
redis-cli ping                                  # Redis
```

### 监控界面
- API文档：`http://localhost:8000/docs`（Swagger）
- Celery任务：`http://localhost:5555`（Flower）
- ChromaDB：`http://localhost:8001`

### 服务端口
- Deep服务：8000（API网关）
- 嵌入服务：8004（BGE-M3 GPU）
- ChromaDB：8001（向量数据库）
- Redis：6379（消息队列）
- Flower：5555（任务监控）

## 常见开发模式

### 错误处理
服务使用 `core/error_handler.py` 中的集中错误处理，提供结构化错误响应。

### 异步任务管理
- Celery任务位于 `services/worker_service/tasks/`
- 任务状态跟踪通过 `shared/task_state_manager.py`
- 使用 `@celery_app.task` 装饰器，配合适当的错误处理

### 向量操作
- 通过HTTP API调用嵌入服务获取BGE-M3嵌入
- 通过 `shared/protocols/chroma_client.py` 中的客户端抽象进行ChromaDB操作
- 对大型文档集进行批量处理

## 项目特色功能

### 风险预测系统
- **AI驱动分析**：无需预设字段映射，AI自动分析Excel中的风控特征数据
- **文件级隔离**：每个Excel文件创建独立的向量数据库
- **唯一编码管理**：通过 `file_code` 管理不同客户数据的完全隔离
- **灵活数据格式**：AI自动适应不同结构的Excel风控数据表

### RAG问答系统
- **多模型支持**：支持DeepSeek、OpenAI等多种大语言模型
- **本地知识库**：支持PDF、DOCX、TXT、MD等多种文档格式
- **精准检索**：使用BGE-M3嵌入模型进行语义搜索
- **流式输出**：支持实时流式响应

### 开发注意事项
在此代码库中工作时，请始终考虑微服务架构，确保通过定义的API和协议进行适当的服务通信。遵循现有的配置管理模式和错误处理约定。