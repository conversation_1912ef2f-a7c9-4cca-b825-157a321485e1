#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to identify and fix PostHog/ChromaDB telemetry issues
"""

import os
import sys
from pathlib import Path

def set_chromadb_telemetry_env_vars():
    """Set all known ChromaDB telemetry environment variables"""
    
    telemetry_vars = {
        # ChromaDB specific telemetry disable flags
        'ANONYMIZED_TELEMETRY': 'False',
        'CHROMA_ANONYMIZED_TELEMETRY': 'False', 
        'CHROMA_DISABLE_TELEMETRY': 'True',
        'CHROMA_TELEMETRY_DISABLED': 'True',
        
        # PostHog specific disable flags
        'POSTHOG_DISABLED': 'True',
        'POSTHOG_FEATURE_FLAGS_DISABLED': 'True',
        'DISABLE_POSTHOG': 'True',
        
        # General telemetry disable flags
        'DISABLE_TELEMETRY': '1',
        'NO_ANALYTICS': '1',
        'NO_TELEMETRY': '1',
        'TELEMETRY_DISABLED': '1',
    }
    
    print("Setting telemetry environment variables:")
    for var, value in telemetry_vars.items():
        os.environ[var] = value
        print(f"  {var}={value}")
    
    return telemetry_vars

def update_env_files():
    """Update .env files with telemetry disable settings"""
    
    project_root = Path(__file__).parent
    
    env_files = [
        project_root / ".env",
        project_root / "services" / "worker_service" / ".env",
        project_root / "services" / "embedding_service" / ".env",
        project_root / "services" / "deep_service" / ".env",
        project_root / "deployment" / ".env",
    ]
    
    telemetry_section = """
# =============================================================================
# ChromaDB / PostHog Telemetry Disable Settings
# =============================================================================
# ChromaDB telemetry settings
ANONYMIZED_TELEMETRY=False
CHROMA_ANONYMIZED_TELEMETRY=False
CHROMA_DISABLE_TELEMETRY=True
CHROMA_TELEMETRY_DISABLED=True

# PostHog telemetry settings  
POSTHOG_DISABLED=True
POSTHOG_FEATURE_FLAGS_DISABLED=True
DISABLE_POSTHOG=True

# General telemetry disable flags
DISABLE_TELEMETRY=1
NO_ANALYTICS=1
NO_TELEMETRY=1
TELEMETRY_DISABLED=1
"""
    
    for env_file in env_files:
        if env_file.exists():
            print(f"Updating {env_file}")
            
            # Read existing content
            content = env_file.read_text()
            
            # Check if telemetry section already exists
            if "ChromaDB / PostHog Telemetry" not in content:
                # Append telemetry section
                content = content.rstrip() + telemetry_section
                env_file.write_text(content)
                print(f"  Added telemetry settings to {env_file}")
            else:
                print(f"  Telemetry settings already exist in {env_file}")
        else:
            print(f"Creating {env_file}")
            env_file.parent.mkdir(parents=True, exist_ok=True)
            env_file.write_text(telemetry_section.lstrip())

def test_chromadb_import():
    """Test ChromaDB import with telemetry disabled"""
    print("\nTesting ChromaDB import with telemetry disabled...")
    
    # Set environment variables first
    set_chromadb_telemetry_env_vars()
    
    try:
        # Import ChromaDB
        import chromadb
        print("✅ ChromaDB imported successfully")
        
        # Try to create a client
        client = chromadb.Client()
        print("✅ ChromaDB client created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ ChromaDB import/client creation failed: {e}")
        return False

def check_posthog_connections():
    """Check for PostHog-related network connections"""
    print("\nChecking for PostHog network connections...")
    
    import subprocess
    import time
    
    try:
        # Check for active connections to PostHog
        result = subprocess.run(
            ["lsof", "-i", "-n", "-P"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        posthog_connections = [
            line for line in result.stdout.split('\n') 
            if 'posthog.com' in line.lower()
        ]
        
        if posthog_connections:
            print("❌ Found PostHog connections:")
            for conn in posthog_connections:
                print(f"  {conn}")
        else:
            print("✅ No PostHog connections found")
            
    except subprocess.TimeoutExpired:
        print("⚠️  Connection check timed out")
    except FileNotFoundError:
        print("⚠️  lsof command not found, skipping connection check")
    except Exception as e:
        print(f"⚠️  Connection check failed: {e}")

def main():
    """Main function"""
    print("=" * 60)
    print("ChromaDB/PostHog Telemetry Fix Script")
    print("=" * 60)
    
    # Step 1: Set environment variables
    set_chromadb_telemetry_env_vars()
    
    # Step 2: Update .env files
    print("\nUpdating .env files...")
    update_env_files()
    
    # Step 3: Test ChromaDB import
    success = test_chromadb_import()
    
    # Step 4: Check for PostHog connections
    check_posthog_connections()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Telemetry fix completed successfully")
        print("\nNext steps:")
        print("1. Restart all services to pick up new environment variables")
        print("2. Monitor logs for SSL connection warnings")
        print("3. If issues persist, consider downgrading ChromaDB or using alternative vector DB")
    else:
        print("❌ Telemetry fix encountered issues")
        print("\nTroubleshooting:")
        print("1. Check if all dependencies are installed correctly") 
        print("2. Verify environment variables are loaded")
        print("3. Consider using ChromaDB in server mode instead of embedded")
    print("=" * 60)

if __name__ == "__main__":
    main()