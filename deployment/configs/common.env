# Deep Risk RAG 微服务通用环境配置
# 所有服务共享的基础配置

# =============================================================================
# 基础服务配置
# =============================================================================
# 部署环境 (development/staging/production)
ENVIRONMENT=development

# 部署模式 (standalone/docker/k8s)
DEPLOYMENT_MODE=standalone

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=text

# =============================================================================
# 外部服务连接
# =============================================================================
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379/0

# ChromaDB配置
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_API_VERSION=v1
CHROMA_URL=http://localhost:8001

# 嵌入服务配置
EMBEDDING_SERVICE_HOST=localhost
EMBEDDING_SERVICE_PORT=8004
EMBEDDING_SERVICE_URL=http://localhost:8004

# =============================================================================
# LLM配置
# =============================================================================
# LLM提供商 (deepseek/openai/claude/gemini)
LLM_PROVIDER=deepseek

# API密钥
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# 模型配置
LLM_MODEL=deepseek-chat
LLM_MAX_TOKENS=4000
LLM_TEMPERATURE=0.1

# 流式输出
ENABLE_STREAMING=true

# =============================================================================
# 任务配置
# =============================================================================
# 任务超时时间 (秒)
TASK_TIMEOUT=3600

# 最大重试次数
TASK_RETRY_MAX=3

# =============================================================================
# 监控配置
# =============================================================================
# 启用Prometheus指标
ENABLE_METRICS=false

# Prometheus端口
METRICS_PORT=9090

# 健康检查间隔 (秒)
HEALTH_CHECK_INTERVAL=30

# =============================================================================
# ChromaDB / PostHog Telemetry Disable Settings
# =============================================================================
# ChromaDB telemetry settings
ANONYMIZED_TELEMETRY=False
CHROMA_ANONYMIZED_TELEMETRY=False
CHROMA_DISABLE_TELEMETRY=True
CHROMA_TELEMETRY_DISABLED=True

# PostHog telemetry settings  
POSTHOG_DISABLED=True
POSTHOG_FEATURE_FLAGS_DISABLED=True
DISABLE_POSTHOG=True

# General telemetry disable flags
DISABLE_TELEMETRY=1
NO_ANALYTICS=1
NO_TELEMETRY=1
TELEMETRY_DISABLED=1

# =============================================================================
# 安全配置
# =============================================================================
# 允许的来源 (CORS)
ALLOWED_ORIGINS=*

# 请求速率限制 (每分钟)
RATE_LIMIT=100
