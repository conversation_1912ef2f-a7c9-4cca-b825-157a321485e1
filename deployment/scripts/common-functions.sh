#!/bin/bash

# Deep Risk RAG 微服务通用函数库
# 为各服务启动脚本提供公共函数

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 通用打印函数
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# 检查Python环境
check_python() {
    print_header "🐍 检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3未安装"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    print_message "Python版本: $python_version"
    
    # 检查Python版本是否满足要求
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 10) else 1)"; then
        print_error "需要Python 3.10或更高版本"
        exit 1
    fi
}

# 检查基础依赖
check_base_dependencies() {
    local service_name=$1
    print_header "📦 检查${service_name}依赖..."
    
    # 检查requirements.txt
    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt文件不存在"
        exit 1
    fi
}

# 检查Redis连接
check_redis() {
    local redis_host=${REDIS_HOST:-localhost}
    local redis_port=${REDIS_PORT:-6379}
    
    if command -v redis-cli &> /dev/null; then
        if redis-cli -h $redis_host -p $redis_port ping &> /dev/null; then
            print_message "Redis连接正常: $redis_host:$redis_port"
            return 0
        else
            print_warning "Redis连接失败: $redis_host:$redis_port"
            return 1
        fi
    else
        print_warning "redis-cli未安装，无法检查Redis连接"
        return 1
    fi
}

# 检查ChromaDB连接
check_chromadb() {
    local chroma_host=${CHROMA_HOST:-localhost}
    local chroma_port=${CHROMA_PORT:-8001}
    
    if command -v curl &> /dev/null; then
        if curl -f -s "http://$chroma_host:$chroma_port/api/v1/heartbeat" &> /dev/null; then
            print_message "ChromaDB连接正常: $chroma_host:$chroma_port"
            return 0
        else
            print_warning "ChromaDB连接失败: $chroma_host:$chroma_port"
            return 1
        fi
    else
        print_warning "curl未安装，无法检查ChromaDB连接"
        return 1
    fi
}

# 检查嵌入服务连接
check_embedding_service() {
    local embedding_url=${EMBEDDING_SERVICE_URL:-http://localhost:8004}
    
    if command -v curl &> /dev/null; then
        if curl -f -s "$embedding_url/api/v1/health/ready" &> /dev/null; then
            print_message "嵌入服务连接正常: $embedding_url"
            return 0
        else
            print_warning "嵌入服务连接失败: $embedding_url"
            return 1
        fi
    else
        print_warning "curl未安装，无法检查嵌入服务连接"
        return 1
    fi
}

# 检查配置
check_common_configuration() {
    print_header "⚙️ 检查通用配置..."
    
    # 检查必需的环境变量
    if [ -z "$DEEPSEEK_API_KEY" ]; then
        print_warning "DEEPSEEK_API_KEY未设置"
    else
        print_message "DEEPSEEK_API_KEY已设置"
    fi
    
    # 检查Redis URL
    if [ -z "$REDIS_URL" ]; then
        print_warning "REDIS_URL未设置，使用默认值"
    else
        print_message "REDIS_URL已设置: $REDIS_URL"
    fi
}

# 安装依赖
install_dependencies() {
    local service_name=$1
    print_header "📦 安装${service_name}依赖..."
    
    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt文件不存在"
        exit 1
    fi
    
    print_message "安装Python依赖..."
    pip3 install -r requirements.txt
    
    print_message "依赖安装完成"
}

# 设置通用环境变量
setup_common_environment() {
    print_header "⚙️ 设置通用环境..."
    
    # 设置默认环境变量
    export LOG_LEVEL=${LOG_LEVEL:-"INFO"}
    export PYTHONPATH="${PYTHONPATH}:$(pwd):$(pwd)/../.."
    export REDIS_URL=${REDIS_URL:-"redis://localhost:6379/0"}
    
    print_message "日志级别: $LOG_LEVEL"
    print_message "Python路径: $PYTHONPATH"
    print_message "Redis URL: $REDIS_URL"
}

# 创建必要目录
create_directories() {
    local service_name=$1
    print_message "创建${service_name}必要目录..."
    
    mkdir -p logs pids data/uploads cache
}

# 运行测试
run_tests() {
    local service_name=$1
    print_header "🧪 运行${service_name}测试..."
    
    if command -v pytest &> /dev/null; then
        pytest tests/ -v
    else
        print_warning "pytest未安装，跳过测试"
    fi
}

# 显示服务信息
show_service_info() {
    local service_name=$1
    local host=$2
    local port=$3
    
    print_header "🌐 ${service_name}服务信息:"
    echo ""
    echo "📱 服务地址: http://${host}:${port}"
    echo "📋 健康检查: http://${host}:${port}/health"
    echo "📖 API文档: http://${host}:${port}/docs"
    echo ""
}

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local host=$2
    local port=$3
    local health_path=${4:-"/health"}
    
    if command -v curl &> /dev/null; then
        if curl -f -s "http://${host}:${port}${health_path}" &> /dev/null; then
            print_message "${service_name}服务健康检查通过"
            return 0
        else
            print_warning "${service_name}服务健康检查失败"
            return 1
        fi
    else
        print_warning "curl未安装，无法检查${service_name}服务健康状态"
        return 1
    fi
}

# 等待服务启动
wait_for_service() {
    local service_name=$1
    local host=$2
    local port=$3
    local health_path=${4:-"/health"}
    local timeout=${5:-60}
    
    print_message "等待${service_name}服务启动..."
    
    local count=0
    while [ $count -lt $timeout ]; do
        if check_service_health "$service_name" "$host" "$port" "$health_path"; then
            print_message "${service_name}服务启动成功"
            return 0
        fi
        
        sleep 2
        count=$((count + 2))
    done
    
    print_error "${service_name}服务启动超时"
    return 1
}

# 加载环境配置文件
load_env_file() {
    local env_file=${1:-.env}
    
    if [ -f "$env_file" ]; then
        print_message "加载环境配置文件: $env_file"
        set -a
        source "$env_file"
        set +a
    else
        print_warning "环境配置文件不存在: $env_file"
    fi
}

# 检查端口占用
check_port() {
    local port=$1
    local service_name=$2
    
    if command -v lsof &> /dev/null; then
        if lsof -i :$port &> /dev/null; then
            print_warning "端口 $port 已被占用，${service_name}可能无法启动"
            return 1
        else
            print_message "端口 $port 可用"
            return 0
        fi
    else
        print_warning "lsof未安装，无法检查端口占用"
        return 0
    fi
}
