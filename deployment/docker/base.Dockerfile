# Deep Risk RAG 基础镜像
# 包含所有服务的通用依赖和配置

FROM python:3.10-slim as base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 复制通用requirements
COPY deployment/requirements/common.txt ./requirements-common.txt

# 安装通用Python依赖
RUN pip install --no-cache-dir -r requirements-common.txt

# 创建通用目录结构
RUN mkdir -p /app/data/uploads /app/logs /app/cache /app/pids && \
    chown -R appuser:appuser /app

# 设置通用环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 切换到非root用户
USER appuser

# 健康检查基础脚本
COPY --chown=appuser:appuser deployment/scripts/base-healthcheck.sh /app/base-healthcheck.sh
RUN chmod +x /app/base-healthcheck.sh
