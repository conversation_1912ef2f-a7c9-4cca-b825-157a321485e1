version: '3.8'

# 扩展基础设施服务
include:
  - docker-compose.base.yml

services:

  # BGE-M3 嵌入服务 (GPU)
  embedding-service:
    build:
      context: ../
      dockerfile: deployment/docker/embedding-service.Dockerfile
    container_name: deep-risk-embedding
    ports:
      - "8004:8004"
    env_file:
      - configs/common.env
    environment:
      - HOST=0.0.0.0
      - PORT=8004
      - LOG_LEVEL=INFO
      - DEVICE=auto  # 自动检测GPU/CPU
    volumes:
      - ../models:/app/models:ro
      - embedding_cache:/app/cache
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/api/v1/health/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - deep-risk-network

  # Deep服务 - 统一API网关
  deep-service:
    build:
      context: ../
      dockerfile: deployment/docker/deep-service.Dockerfile
    container_name: deep-risk-deep
    ports:
      - "8000:8000"
    env_file:
      - configs/common.env
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=INFO
      - REDIS_URL=redis://redis:6379/0
      - CHROMA_URL=http://chromadb:8000
      - EMBEDDING_SERVICE_URL=http://embedding-service:8004
    volumes:
      - ../data/uploads:/app/data/uploads
      - deep_logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
      chromadb:
        condition: service_healthy
      embedding-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - deep-risk-network

  # Worker服务 - 后台任务处理
  worker-service:
    build:
      context: ../
      dockerfile: deployment/docker/worker-service.Dockerfile
    container_name: deep-risk-worker
    env_file:
      - configs/common.env
    environment:
      - LOG_LEVEL=INFO
      - REDIS_URL=redis://redis:6379/0
      - CHROMA_URL=http://chromadb:8000
      - EMBEDDING_SERVICE_URL=http://embedding-service:8004
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
    volumes:
      - ../data/uploads:/app/data/uploads
      - worker_logs:/app/logs
      - ../prompts:/app/prompts:ro
    depends_on:
      redis:
        condition: service_healthy
      chromadb:
        condition: service_healthy
      embedding-service:
        condition: service_healthy
    networks:
      - deep-risk-network

  # Flower - Celery任务监控
  flower:
    build:
      context: ../
      dockerfile: deployment/docker/worker-service.Dockerfile
    container_name: deep-risk-flower
    ports:
      - "5555:5555"
    env_file:
      - configs/common.env
    environment:
      - LOG_LEVEL=INFO
      - REDIS_URL=redis://redis:6379/0
    command: celery -A services.worker_service.worker flower --port=5555
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - deep-risk-network

volumes:
  redis_data:
  chromadb_data:
  embedding_cache:
  deep_logs:
  worker_logs:

networks:
  deep-risk-network:
    driver: bridge
