version: '3.8'

# 基础设施服务
services:
  # Redis - 消息队列和缓存
  redis:
    image: redis:7-alpine
    container_name: deep-risk-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./infrastructure/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - deep-risk-network

  # ChromaDB - 向量数据库
  chromadb:
    image: chromadb/chroma:latest
    container_name: deep-risk-chromadb
    ports:
      - "${CHROMA_PORT:-8001}:8000"
    volumes:
      - chromadb_data:/chroma/chroma
      - ./infrastructure/chromadb/chroma.conf:/chroma/chroma.conf:ro
    env_file:
      - configs/common.env
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - deep-risk-network

volumes:
  redis_data:
    driver: local
  chromadb_data:
    driver: local

networks:
  deep-risk-network:
    driver: bridge
