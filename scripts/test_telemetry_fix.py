#!/usr/bin/env python3
"""
测试遥测禁用是否生效
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入遥测禁用工具
from shared.utils.telemetry_disable import disable_all_telemetry

# 确保遥测被禁用
disable_all_telemetry()

def test_env_vars():
    """测试环境变量是否正确设置"""
    print("🔍 检查遥测禁用环境变量...")
    
    critical_vars = [
        "POSTHOG_HOST",
        "POSTHOG_PROJECT_ID", 
        "POSTHOG_API_KEY",
        "LANGCHAIN_TRACING_V2",
        "LANGCHAIN_ANALYTICS",
        "TELEMETRY_DISABLED",
        "DO_NOT_TRACK"
    ]
    
    all_good = True
    for var in critical_vars:
        value = os.environ.get(var, "未设置")
        expected = "" if var.startswith(("POSTHOG_", "LANGCHAIN_")) and not var.endswith("_DISABLED") else "true" if var in ["TELEMETRY_DISABLED"] else "1" if var == "DO_NOT_TRACK" else "false"
        
        if var.startswith("POSTHOG_") and var != "POSTHOG_FEATURE_FLAGS":
            is_ok = value == ""
        elif var == "POSTHOG_FEATURE_FLAGS":
            is_ok = value == "false"
        elif var.startswith("LANGCHAIN_"):
            is_ok = value == "false"
        elif var == "TELEMETRY_DISABLED":
            is_ok = value == "true"
        elif var == "DO_NOT_TRACK":
            is_ok = value == "1"
        else:
            is_ok = True
            
        status = "✅" if is_ok else "❌"
        print(f"  {status} {var}: '{value}'")
        
        if not is_ok:
            all_good = False
    
    return all_good

def test_langchain_import():
    """测试导入LangChain是否会触发遥测"""
    print("\n🧪 测试LangChain导入...")
    
    try:
        # 模拟worker service中的导入
        from services.worker_service.core.vectorizer import VectorizationProcessor
        print("✅ VectorizationProcessor导入成功，无遥测连接")
        return True
    except Exception as e:
        print(f"❌ VectorizationProcessor导入失败: {e}")
        return False

def test_shared_llm_import():
    """测试导入shared LLM模块是否会触发遥测"""
    print("\n🧪 测试Shared LLM模块导入...")
    
    try:
        from shared.llm import LLMFactory, create_llm_from_settings
        print("✅ Shared LLM模块导入成功，无遥测连接")
        return True
    except Exception as e:
        print(f"❌ Shared LLM模块导入失败: {e}")
        return False

def test_worker_import():
    """测试导入worker模块是否会触发遥测"""
    print("\n🧪 测试Worker模块导入...")
    
    try:
        from services.worker_service.worker import celery_app
        print("✅ Worker模块导入成功，无遥测连接")
        return True
    except Exception as e:
        print(f"❌ Worker模块导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试PostHog遥测禁用效果...")
    print("=" * 60)
    
    # 测试环境变量
    env_ok = test_env_vars()
    
    # 测试模块导入
    vectorizer_ok = test_langchain_import()
    llm_ok = test_shared_llm_import()
    worker_ok = test_worker_import()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"  环境变量设置: {'✅' if env_ok else '❌'}")
    print(f"  VectorizationProcessor: {'✅' if vectorizer_ok else '❌'}")
    print(f"  Shared LLM模块: {'✅' if llm_ok else '❌'}")
    print(f"  Worker模块: {'✅' if worker_ok else '❌'}")
    
    all_tests_passed = all([env_ok, vectorizer_ok, llm_ok, worker_ok])
    
    if all_tests_passed:
        print("\n🎉 所有测试通过！PostHog遥测已成功禁用。")
        print("💡 建议重启worker服务以确保生效。")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main())